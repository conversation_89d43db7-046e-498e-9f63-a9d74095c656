"""
文件类型检测器
"""

import zipfile
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import magic

from .config import config
from .logger import get_logger
from .models import FileType

logger = get_logger(__name__)


class FileTypeDetector:
    """文件类型检测器"""
    
    # 文件头标志映射
    SIGNATURES = {
        # PDF
        b'%PDF': FileType.PDF,
        
        # Microsoft Office (OLE2 格式)
        b'\xd0\xcf\x11\xe0\xa1\xb1\x1a\xe1': 'OLE2',
        
        # ZIP 格式文件
        b'PK\x03\x04': 'ZIP',
        
        # WPS文件
        b'\xd0\xcf\x11\xe0': FileType.WPS,
        
        # CEB文件 (方正Apabi格式)
        b'CEB\x00': FileType.CEB,
        b'\x43\x45\x42': FileType.CEB,
        
        # 文本文件 BOM
        b'\xef\xbb\xbf': FileType.TXT,  # UTF-8 BOM
        b'\xff\xfe': FileType.TXT,      # UTF-16 LE BOM
        b'\xfe\xff': FileType.TXT,      # UTF-16 BE BOM
        
        # RTF文件
        b'{\\rtf': FileType.TXT,
        
        # HTML文件
        b'<!DOCTYPE html': FileType.TXT,
        b'<html': FileType.TXT,
        b'<HTML': FileType.TXT,
    }
    
    # MIME类型映射
    MIME_TYPES = {
        'application/pdf': FileType.PDF,
        'application/epub+zip': FileType.EPUB,
        'application/msword': FileType.DOC,
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document': FileType.DOCX,
        'application/vnd.ms-powerpoint': FileType.PPT,
        'application/vnd.openxmlformats-officedocument.presentationml.presentation': FileType.PPTX,
        'text/plain': FileType.TXT,
        'text/markdown': FileType.MD,
        'text/html': FileType.TXT,
        'text/rtf': FileType.TXT,
        'application/rtf': FileType.TXT,
    }
    
    # 扩展名映射
    EXTENSIONS = {
        '.txt': FileType.TXT,
        '.pdf': FileType.PDF,
        '.epub': FileType.EPUB,
        '.doc': FileType.DOC,
        '.docx': FileType.DOCX,
        '.wps': FileType.WPS,
        '.ceb': FileType.CEB,
        '.ppt': FileType.PPT,
        '.pptx': FileType.PPTX,
        '.md': FileType.MD,
        '.markdown': FileType.MD,
        '.html': FileType.TXT,
        '.htm': FileType.TXT,
        '.rtf': FileType.TXT,
    }
    
    def __init__(self):
        self.detection_cache = {}  # 缓存检测结果
    
    def detect_file_type(self, file_path: Path) -> FileType:
        """检测文件类型"""
        if not file_path.exists() or not file_path.is_file():
            return FileType.UNKNOWN
        
        # 检查缓存
        file_key = str(file_path)
        if file_key in self.detection_cache:
            return self.detection_cache[file_key]
        
        file_type = self._detect_file_type_internal(file_path)
        
        # 缓存结果
        self.detection_cache[file_key] = file_type
        
        return file_type
    
    def _detect_file_type_internal(self, file_path: Path) -> FileType:
        """内部文件类型检测逻辑"""
        try:
            # 1. 通过文件头标志检测
            file_type = self._detect_by_signature(file_path)
            if file_type != FileType.UNKNOWN:
                return file_type
            
            # 2. 通过MIME类型检测
            file_type = self._detect_by_mime_type(file_path)
            if file_type != FileType.UNKNOWN:
                return file_type
            
            # 3. 通过扩展名检测
            file_type = self._detect_by_extension(file_path)
            if file_type != FileType.UNKNOWN:
                return file_type
            
            # 4. 通过内容特征检测
            file_type = self._detect_by_content(file_path)
            if file_type != FileType.UNKNOWN:
                return file_type
            
            return FileType.UNKNOWN
            
        except Exception as e:
            logger.error(f"文件类型检测失败 {file_path}: {e}")
            return FileType.UNKNOWN
    
    def _detect_by_signature(self, file_path: Path) -> FileType:
        """通过文件头标志检测"""
        try:
            with open(file_path, 'rb') as f:
                header = f.read(64)  # 读取前64字节
            
            for signature, file_type in self.SIGNATURES.items():
                if header.startswith(signature):
                    if file_type == 'OLE2':
                        return self._detect_ole2_format(file_path)
                    elif file_type == 'ZIP':
                        return self._detect_zip_format(file_path)
                    else:
                        return file_type
            
            return FileType.UNKNOWN
            
        except Exception as e:
            logger.debug(f"文件头检测失败 {file_path}: {e}")
            return FileType.UNKNOWN
    
    def _detect_ole2_format(self, file_path: Path) -> FileType:
        """检测OLE2格式的具体类型"""
        try:
            # 可以通过读取OLE2结构来判断具体类型
            # 这里简化处理，根据扩展名判断
            extension = file_path.suffix.lower()
            if extension == '.doc':
                return FileType.DOC
            elif extension == '.ppt':
                return FileType.PPT
            elif extension == '.wps':
                return FileType.WPS
            else:
                return FileType.DOC  # 默认为DOC
                
        except Exception as e:
            logger.debug(f"OLE2格式检测失败 {file_path}: {e}")
            return FileType.DOC
    
    def _detect_zip_format(self, file_path: Path) -> FileType:
        """检测ZIP格式的具体类型"""
        try:
            with zipfile.ZipFile(file_path, 'r') as zip_file:
                file_list = zip_file.namelist()
                
                # 检查EPUB格式
                if self._is_epub(file_list, zip_file):
                    return FileType.EPUB
                
                # 检查Office格式
                office_type = self._detect_office_format(file_list, zip_file)
                if office_type != FileType.UNKNOWN:
                    return office_type
                
                # 其他ZIP格式文件，根据扩展名判断
                extension = file_path.suffix.lower()
                if extension in self.EXTENSIONS:
                    return self.EXTENSIONS[extension]
            
            return FileType.UNKNOWN
            
        except zipfile.BadZipFile:
            return FileType.UNKNOWN
        except Exception as e:
            logger.debug(f"ZIP格式检测失败 {file_path}: {e}")
            return FileType.UNKNOWN
    
    def _is_epub(self, file_list: List[str], zip_file: zipfile.ZipFile) -> bool:
        """检查是否为EPUB格式"""
        # EPUB必须包含mimetype文件和META-INF/container.xml
        if 'mimetype' not in file_list or 'META-INF/container.xml' not in file_list:
            return False
        
        try:
            # 检查mimetype文件内容
            mimetype_content = zip_file.read('mimetype').decode('utf-8').strip()
            return mimetype_content == 'application/epub+zip'
        except:
            return False
    
    def _detect_office_format(self, file_list: List[str], zip_file: zipfile.ZipFile) -> FileType:
        """检测Office格式"""
        # 检查DOCX格式
        if 'word/document.xml' in file_list:
            return FileType.DOCX
        
        # 检查PPTX格式
        if 'ppt/presentation.xml' in file_list or any('ppt/slides/' in f for f in file_list):
            return FileType.PPTX
        
        # 通过Content_Types.xml检测
        if '[Content_Types].xml' in file_list:
            try:
                content_types = zip_file.read('[Content_Types].xml').decode('utf-8')
                if 'wordprocessingml' in content_types:
                    return FileType.DOCX
                elif 'presentationml' in content_types:
                    return FileType.PPTX
            except:
                pass
        
        return FileType.UNKNOWN
    
    def _detect_by_mime_type(self, file_path: Path) -> FileType:
        """通过MIME类型检测"""
        try:
            mime_type = magic.from_file(str(file_path), mime=True)
            return self.MIME_TYPES.get(mime_type, FileType.UNKNOWN)
        except Exception as e:
            logger.debug(f"MIME类型检测失败 {file_path}: {e}")
            return FileType.UNKNOWN
    
    def _detect_by_extension(self, file_path: Path) -> FileType:
        """通过扩展名检测"""
        extension = file_path.suffix.lower()
        return self.EXTENSIONS.get(extension, FileType.UNKNOWN)
    
    def _detect_by_content(self, file_path: Path) -> FileType:
        """通过内容特征检测"""
        try:
            # 读取文件开头内容
            with open(file_path, 'rb') as f:
                content = f.read(1024)
            
            # 尝试解码为文本
            try:
                text_content = content.decode('utf-8')
                
                # 检查Markdown特征
                if self._is_markdown_content(text_content):
                    return FileType.MD
                
                # 检查HTML特征
                if self._is_html_content(text_content):
                    return FileType.TXT
                
                # 如果能正常解码且包含可打印字符，认为是文本文件
                if any(c.isprintable() for c in text_content):
                    return FileType.TXT
                    
            except UnicodeDecodeError:
                # 尝试其他编码
                for encoding in ['gbk', 'gb2312', 'latin1']:
                    try:
                        text_content = content.decode(encoding)
                        if any(c.isprintable() for c in text_content):
                            return FileType.TXT
                    except:
                        continue
            
            return FileType.UNKNOWN
            
        except Exception as e:
            logger.debug(f"内容检测失败 {file_path}: {e}")
            return FileType.UNKNOWN
    
    def _is_markdown_content(self, content: str) -> bool:
        """检查是否为Markdown内容"""
        markdown_indicators = [
            '# ', '## ', '### ',  # 标题
            '- ', '* ', '+ ',     # 列表
            '```',                # 代码块
            '[', '](',            # 链接
            '**', '__',           # 粗体
            '*', '_',             # 斜体
        ]
        
        return any(indicator in content for indicator in markdown_indicators)
    
    def _is_html_content(self, content: str) -> bool:
        """检查是否为HTML内容"""
        html_tags = ['<html', '<head', '<body', '<div', '<p', '<span', '<a']
        content_lower = content.lower()
        return any(tag in content_lower for tag in html_tags)
    
    def is_supported_file(self, file_path: Path) -> bool:
        """检查文件是否为支持的类型"""
        if not file_path.exists() or not file_path.is_file():
            return False
        
        # 检查文件大小
        try:
            file_size = file_path.stat().st_size
            if file_size > config.max_file_size:
                logger.warning(f"文件过大，跳过: {file_path} ({file_size} bytes)")
                return False
        except Exception:
            return False
        
        # 检测文件类型
        file_type = self.detect_file_type(file_path)
        return file_type != FileType.UNKNOWN
    
    def get_file_info(self, file_path: Path) -> Dict:
        """获取文件详细信息"""
        try:
            stat = file_path.stat()
            file_type = self.detect_file_type(file_path)
            
            return {
                'path': str(file_path),
                'name': file_path.name,
                'size': stat.st_size,
                'created': stat.st_ctime,
                'modified': stat.st_mtime,
                'extension': file_path.suffix.lower(),
                'file_type': file_type,
                'is_supported': file_type != FileType.UNKNOWN,
            }
        except Exception as e:
            logger.error(f"获取文件信息失败 {file_path}: {e}")
            return {}
    
    def clear_cache(self):
        """清空检测缓存"""
        self.detection_cache.clear()


# 全局文件类型检测器实例
file_detector = FileTypeDetector()
