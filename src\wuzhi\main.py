"""
主应用入口
"""

import typer
from pathlib import Path
from rich.console import Console
from rich.panel import Panel
from rich.text import Text

from .core.config import config
from .core.logger import get_logger
from .ui.app import WuZhiApp

logger = get_logger(__name__)
console = Console()

app = typer.Typer(
    name="wuzhi",
    help="悟知 (<PERSON><PERSON><PERSON>) - 个人知识管理系统",
    add_completion=False,
)


@app.command()
def gui(
    debug: bool = typer.Option(False, "--debug", "-d", help="启用调试模式"),
    port: int = typer.Option(8080, "--port", "-p", help="Web端口号"),
) -> None:
    """启动图形界面"""
    try:
        # 显示启动信息
        title = Text("悟知 (WuZhi)", style="bold blue")
        subtitle = Text("个人知识管理系统", style="italic")
        version_text = Text(f"版本: {config.version}", style="dim")
        
        panel_content = f"{title}\n{subtitle}\n{version_text}"
        console.print(Panel(panel_content, title="启动中...", border_style="blue"))
        
        # 设置调试模式
        if debug:
            config.debug = True
            logger.info("调试模式已启用")
        
        # 启动应用
        logger.info(f"启动悟知应用，端口: {port}")
        app_instance = WuZhiApp()
        app_instance.run(port=port)
        
    except KeyboardInterrupt:
        logger.info("用户中断，正在退出...")
        console.print("\n[yellow]应用已停止[/yellow]")
    except Exception as e:
        logger.error(f"启动失败: {e}")
        console.print(f"[red]启动失败: {e}[/red]")
        raise typer.Exit(1)


@app.command()
def scan(
    path: str = typer.Argument(..., help="要扫描的目录路径"),
    recursive: bool = typer.Option(True, "--recursive", "-r", help="递归扫描子目录"),
    analyze: bool = typer.Option(False, "--analyze", "-a", help="立即分析文档"),
) -> None:
    """扫描指定目录中的文档"""
    try:
        scan_path = Path(path)
        if not scan_path.exists():
            console.print(f"[red]路径不存在: {path}[/red]")
            raise typer.Exit(1)
        
        if not scan_path.is_dir():
            console.print(f"[red]不是有效目录: {path}[/red]")
            raise typer.Exit(1)
        
        console.print(f"[blue]开始扫描目录: {path}[/blue]")
        
        # TODO: 实现文档扫描功能
        from .core.scanner import DocumentScanner
        
        scanner = DocumentScanner()
        documents = scanner.scan_directory(scan_path, recursive=recursive)
        
        console.print(f"[green]扫描完成，发现 {len(documents)} 个文档[/green]")
        
        if analyze:
            console.print("[blue]开始分析文档...[/blue]")
            # TODO: 实现文档分析功能
            console.print("[green]分析完成[/green]")
            
    except Exception as e:
        logger.error(f"扫描失败: {e}")
        console.print(f"[red]扫描失败: {e}[/red]")
        raise typer.Exit(1)


@app.command()
def analyze(
    document_id: int = typer.Option(None, "--id", help="分析指定ID的文档"),
    all_docs: bool = typer.Option(False, "--all", help="分析所有未分析的文档"),
    use_ai: bool = typer.Option(None, "--ai", help="使用AI进行分析"),
) -> None:
    """分析文档内容"""
    try:
        if not document_id and not all_docs:
            console.print("[red]请指定要分析的文档ID或使用 --all 分析所有文档[/red]")
            raise typer.Exit(1)
        
        # TODO: 实现文档分析功能
        console.print("[blue]开始分析文档...[/blue]")
        console.print("[green]分析完成[/green]")
        
    except Exception as e:
        logger.error(f"分析失败: {e}")
        console.print(f"[red]分析失败: {e}[/red]")
        raise typer.Exit(1)


@app.command()
def stats() -> None:
    """显示统计信息"""
    try:
        # TODO: 实现统计功能
        console.print("[blue]统计信息:[/blue]")
        console.print("- 总文档数: 0")
        console.print("- 已分析文档数: 0")
        console.print("- 关键词总数: 0")
        console.print("- 重复文档组数: 0")
        
    except Exception as e:
        logger.error(f"获取统计信息失败: {e}")
        console.print(f"[red]获取统计信息失败: {e}[/red]")
        raise typer.Exit(1)


@app.command()
def init() -> None:
    """初始化应用数据库和配置"""
    try:
        console.print("[blue]正在初始化悟知应用...[/blue]")
        
        # 创建数据库表
        from .core.database import init_database
        init_database()
        
        console.print("[green]数据库初始化完成[/green]")
        console.print(f"[blue]配置文件位置: {config.database_path}[/blue]")
        console.print(f"[blue]日志文件位置: {config.log_file}[/blue]")
        console.print("[green]初始化完成！[/green]")
        
    except Exception as e:
        logger.error(f"初始化失败: {e}")
        console.print(f"[red]初始化失败: {e}[/red]")
        raise typer.Exit(1)


@app.command()
def version() -> None:
    """显示版本信息"""
    console.print(f"悟知 (WuZhi) v{config.version}")
    console.print("个人知识管理系统")


if __name__ == "__main__":
    app()
