"""
PDF文件内容提取器
"""

from pathlib import Path
from typing import List, Dict, Any
from datetime import datetime
import PyPDF2
import fitz  # PyMuPDF，备用方案

from .base import BaseExtractor, ExtractionResult
from ..core.models import FileType
from ..core.logger import get_logger

logger = get_logger(__name__)


class PDFExtractor(BaseExtractor):
    """PDF文件提取器"""
    
    def __init__(self):
        super().__init__()
        self.supported_types = [FileType.PDF]
    
    def can_extract(self, file_path: Path, file_type: FileType) -> bool:
        """检查是否可以提取指定文件"""
        return file_type in self.supported_types and file_path.exists()
    
    def extract(self, file_path: Path) -> ExtractionResult:
        """提取PDF文件内容"""
        try:
            # 首先尝试使用PyPDF2
            result = self._extract_with_pypdf2(file_path)
            
            # 如果PyPDF2失败或提取的文本太少，尝试PyMuPDF
            if not result.success or len(result.plain_text.strip()) < 100:
                logger.info(f"PyPDF2提取效果不佳，尝试PyMuPDF: {file_path}")
                result = self._extract_with_pymupdf(file_path)
            
            return result
            
        except Exception as e:
            logger.error(f"PDF文件提取失败 {file_path}: {e}")
            return self._create_error_result(f"提取失败: {e}")
    
    def _extract_with_pypdf2(self, file_path: Path) -> ExtractionResult:
        """使用PyPDF2提取PDF内容"""
        try:
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                
                # 检查PDF是否加密
                if pdf_reader.is_encrypted:
                    logger.warning(f"PDF文件已加密: {file_path}")
                    return self._create_error_result("PDF文件已加密")
                
                # 提取元数据
                metadata = self._extract_pdf_metadata(pdf_reader)
                
                # 提取文本内容
                text_content = ""
                page_texts = []
                
                for page_num, page in enumerate(pdf_reader.pages):
                    try:
                        page_text = page.extract_text()
                        if page_text:
                            text_content += page_text + "\n"
                            page_texts.append(page_text)
                    except Exception as e:
                        logger.warning(f"提取第{page_num+1}页失败: {e}")
                        continue
                
                if not text_content.strip():
                    return self._create_error_result("无法提取文本内容（可能是扫描版PDF）")
                
                # 清理文本
                cleaned_text = self._clean_text(text_content)
                
                # 提取结构化信息
                paragraphs = self._extract_paragraphs(cleaned_text)
                
                return self._create_success_result(
                    text_content=text_content,
                    plain_text=cleaned_text,
                    title=metadata.get('title'),
                    author=metadata.get('author'),
                    subject=metadata.get('subject'),
                    creator=metadata.get('creator'),
                    producer=metadata.get('producer'),
                    creation_date=metadata.get('creation_date'),
                    modification_date=metadata.get('modification_date'),
                    page_count=len(pdf_reader.pages),
                    paragraphs=paragraphs,
                    metadata={
                        'extractor': 'PyPDF2',
                        'page_texts': page_texts,
                        **metadata
                    }
                )
                
        except Exception as e:
            logger.error(f"PyPDF2提取失败 {file_path}: {e}")
            return self._create_error_result(f"PyPDF2提取失败: {e}")
    
    def _extract_with_pymupdf(self, file_path: Path) -> ExtractionResult:
        """使用PyMuPDF提取PDF内容"""
        try:
            doc = fitz.open(str(file_path))
            
            # 提取元数据
            metadata = self._extract_pymupdf_metadata(doc)
            
            # 提取文本内容
            text_content = ""
            page_texts = []
            images = []
            links = []
            
            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                
                # 提取文本
                page_text = page.get_text()
                if page_text:
                    text_content += page_text + "\n"
                    page_texts.append(page_text)
                
                # 提取图片信息
                image_list = page.get_images()
                for img_index, img in enumerate(image_list):
                    images.append({
                        'page': page_num + 1,
                        'index': img_index,
                        'xref': img[0],
                        'width': img[2],
                        'height': img[3],
                    })
                
                # 提取链接
                link_list = page.get_links()
                for link in link_list:
                    links.append({
                        'page': page_num + 1,
                        'uri': link.get('uri', ''),
                        'type': link.get('kind', 0),
                    })
            
            doc.close()
            
            if not text_content.strip():
                return self._create_error_result("无法提取文本内容（可能是扫描版PDF）")
            
            # 清理文本
            cleaned_text = self._clean_text(text_content)
            
            # 提取结构化信息
            paragraphs = self._extract_paragraphs(cleaned_text)
            
            return self._create_success_result(
                text_content=text_content,
                plain_text=cleaned_text,
                title=metadata.get('title'),
                author=metadata.get('author'),
                subject=metadata.get('subject'),
                creator=metadata.get('creator'),
                producer=metadata.get('producer'),
                creation_date=metadata.get('creation_date'),
                modification_date=metadata.get('modification_date'),
                page_count=len(page_texts),
                paragraphs=paragraphs,
                images=images,
                links=links,
                metadata={
                    'extractor': 'PyMuPDF',
                    'page_texts': page_texts,
                    **metadata
                }
            )
            
        except Exception as e:
            logger.error(f"PyMuPDF提取失败 {file_path}: {e}")
            return self._create_error_result(f"PyMuPDF提取失败: {e}")
    
    def _extract_pdf_metadata(self, pdf_reader: PyPDF2.PdfReader) -> Dict[str, Any]:
        """提取PyPDF2的PDF元数据"""
        metadata = {}
        
        try:
            if pdf_reader.metadata:
                pdf_metadata = pdf_reader.metadata
                
                # 提取标准元数据字段
                metadata['title'] = self._clean_metadata_value(pdf_metadata.get('/Title'))
                metadata['author'] = self._clean_metadata_value(pdf_metadata.get('/Author'))
                metadata['subject'] = self._clean_metadata_value(pdf_metadata.get('/Subject'))
                metadata['creator'] = self._clean_metadata_value(pdf_metadata.get('/Creator'))
                metadata['producer'] = self._clean_metadata_value(pdf_metadata.get('/Producer'))
                
                # 提取日期
                creation_date = pdf_metadata.get('/CreationDate')
                if creation_date:
                    metadata['creation_date'] = self._parse_pdf_date(creation_date)
                
                modification_date = pdf_metadata.get('/ModDate')
                if modification_date:
                    metadata['modification_date'] = self._parse_pdf_date(modification_date)
                
                # 其他元数据
                metadata['keywords'] = self._clean_metadata_value(pdf_metadata.get('/Keywords'))
                
        except Exception as e:
            logger.warning(f"提取PDF元数据失败: {e}")
        
        return metadata
    
    def _extract_pymupdf_metadata(self, doc) -> Dict[str, Any]:
        """提取PyMuPDF的PDF元数据"""
        metadata = {}
        
        try:
            pdf_metadata = doc.metadata
            
            metadata['title'] = self._clean_metadata_value(pdf_metadata.get('title'))
            metadata['author'] = self._clean_metadata_value(pdf_metadata.get('author'))
            metadata['subject'] = self._clean_metadata_value(pdf_metadata.get('subject'))
            metadata['creator'] = self._clean_metadata_value(pdf_metadata.get('creator'))
            metadata['producer'] = self._clean_metadata_value(pdf_metadata.get('producer'))
            metadata['keywords'] = self._clean_metadata_value(pdf_metadata.get('keywords'))
            
            # 日期处理
            if pdf_metadata.get('creationDate'):
                metadata['creation_date'] = self._parse_pdf_date(pdf_metadata['creationDate'])
            
            if pdf_metadata.get('modDate'):
                metadata['modification_date'] = self._parse_pdf_date(pdf_metadata['modDate'])
                
        except Exception as e:
            logger.warning(f"提取PyMuPDF元数据失败: {e}")
        
        return metadata
    
    def _clean_metadata_value(self, value) -> str:
        """清理元数据值"""
        if not value:
            return None
        
        if isinstance(value, str):
            # 移除PDF特殊字符
            cleaned = value.strip()
            if cleaned and cleaned != 'None':
                return cleaned
        
        return None
    
    def _parse_pdf_date(self, date_str: str) -> datetime:
        """解析PDF日期格式"""
        if not date_str:
            return None
        
        try:
            # PDF日期格式: D:YYYYMMDDHHmmSSOHH'mm'
            if date_str.startswith('D:'):
                date_str = date_str[2:]
            
            # 提取年月日时分秒
            if len(date_str) >= 14:
                year = int(date_str[0:4])
                month = int(date_str[4:6])
                day = int(date_str[6:8])
                hour = int(date_str[8:10])
                minute = int(date_str[10:12])
                second = int(date_str[12:14])
                
                return datetime(year, month, day, hour, minute, second)
            elif len(date_str) >= 8:
                year = int(date_str[0:4])
                month = int(date_str[4:6])
                day = int(date_str[6:8])
                
                return datetime(year, month, day)
                
        except (ValueError, IndexError) as e:
            logger.debug(f"PDF日期解析失败: {date_str}, {e}")
        
        return None
    
    def _extract_paragraphs(self, text: str) -> List[str]:
        """提取段落"""
        if not text:
            return []
        
        # PDF文本通常有很多换行，需要智能合并
        lines = text.split('\n')
        paragraphs = []
        current_paragraph = []
        
        for line in lines:
            line = line.strip()
            if not line:
                if current_paragraph:
                    paragraphs.append(' '.join(current_paragraph))
                    current_paragraph = []
            else:
                current_paragraph.append(line)
        
        # 添加最后一个段落
        if current_paragraph:
            paragraphs.append(' '.join(current_paragraph))
        
        # 过滤太短的段落并合并可能被错误分割的段落
        filtered_paragraphs = []
        for para in paragraphs:
            if len(para.strip()) > 20:  # 只保留较长的段落
                filtered_paragraphs.append(para)
        
        return filtered_paragraphs
