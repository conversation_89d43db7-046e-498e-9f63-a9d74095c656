"""
文本处理工具函数
"""

import re
import jieba
from collections import Counter
from typing import Dict, List, Optional, Tuple
from langdetect import detect, DetectorFactory
from sklearn.feature_extraction.text import TfidfVectorizer
import nltk
from nltk.corpus import stopwords
from nltk.tokenize import word_tokenize, sent_tokenize

from ..core.config import config
from ..core.logger import get_logger
from ..core.models import Language

logger = get_logger(__name__)

# 设置langdetect的随机种子，确保结果一致性
DetectorFactory.seed = 0

# 中文停用词
CHINESE_STOPWORDS = {
    '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这', '那', '他', '她', '它', '们', '这个', '那个', '什么', '怎么', '为什么', '哪里', '哪个', '怎样', '多少', '几个', '第一', '可以', '应该', '能够', '需要', '必须', '可能', '或者', '但是', '然后', '因为', '所以', '如果', '虽然', '虽说', '不过', '而且', '并且', '以及', '还有', '包括', '比如', '例如', '等等', '之类', '方面', '问题', '情况', '时候', '地方', '方式', '方法', '结果', '原因', '目的', '意思', '内容', '部分', '全部', '一些', '许多', '大量', '少量', '足够', '太多', '过多', '过少'
}

# 英文停用词（需要下载NLTK数据）
try:
    nltk.data.find('tokenizers/punkt')
    nltk.data.find('corpora/stopwords')
except LookupError:
    logger.warning("NLTK数据未找到，正在下载...")
    try:
        nltk.download('punkt', quiet=True)
        nltk.download('stopwords', quiet=True)
    except Exception as e:
        logger.error(f"NLTK数据下载失败: {e}")

ENGLISH_STOPWORDS = set(stopwords.words('english')) if 'stopwords' in dir(nltk.corpus) else set()


def detect_language(text: str) -> Language:
    """检测文本语言"""
    if not text or len(text.strip()) < 10:
        return Language.UNKNOWN
    
    try:
        # 使用langdetect检测语言
        detected = detect(text)
        
        # 映射到我们的语言枚举
        language_map = {
            'zh-cn': Language.CHINESE,
            'zh': Language.CHINESE,
            'en': Language.ENGLISH,
            'ja': Language.JAPANESE,
            'ko': Language.KOREAN,
            'fr': Language.FRENCH,
            'de': Language.GERMAN,
            'es': Language.SPANISH,
            'ru': Language.RUSSIAN,
        }
        
        return language_map.get(detected, Language.UNKNOWN)
        
    except Exception as e:
        logger.debug(f"语言检测失败: {e}")
        
        # 简单的启发式检测
        chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', text))
        total_chars = len(text)
        
        if chinese_chars / total_chars > 0.3:
            return Language.CHINESE
        elif re.search(r'[a-zA-Z]', text):
            return Language.ENGLISH
        
        return Language.UNKNOWN


def clean_text(text: str) -> str:
    """清理文本"""
    if not text:
        return ""
    
    # 移除多余的空白字符
    text = re.sub(r'\s+', ' ', text)
    
    # 移除特殊字符（保留中文、英文、数字、基本标点）
    text = re.sub(r'[^\u4e00-\u9fff\w\s.,!?;:()[\]{}"\'-]', '', text)
    
    # 移除多余的标点符号
    text = re.sub(r'[.,!?;:]{2,}', '.', text)
    
    return text.strip()


def count_words(text: str, language: Language = None) -> Tuple[int, int]:
    """统计字数和字符数"""
    if not text:
        return 0, 0
    
    char_count = len(text)
    
    if language is None:
        language = detect_language(text)
    
    if language == Language.CHINESE:
        # 中文按字符计算
        word_count = len(re.findall(r'[\u4e00-\u9fff]', text))
    else:
        # 其他语言按单词计算
        words = re.findall(r'\b\w+\b', text)
        word_count = len(words)
    
    return word_count, char_count


def extract_keywords_chinese(text: str, max_keywords: int = 20) -> Dict[str, int]:
    """提取中文关键词"""
    try:
        # 使用jieba分词
        words = jieba.cut(text)
        
        # 过滤停用词和短词
        filtered_words = [
            word.strip() for word in words
            if len(word.strip()) > 1 
            and word.strip() not in CHINESE_STOPWORDS
            and not re.match(r'^[0-9\W]+$', word.strip())
        ]
        
        # 统计词频
        word_counts = Counter(filtered_words)
        
        # 返回前N个高频词
        return dict(word_counts.most_common(max_keywords))
        
    except Exception as e:
        logger.error(f"中文关键词提取失败: {e}")
        return {}


def extract_keywords_english(text: str, max_keywords: int = 20) -> Dict[str, int]:
    """提取英文关键词"""
    try:
        # 分词
        words = word_tokenize(text.lower())
        
        # 过滤停用词和短词
        filtered_words = [
            word for word in words
            if len(word) > 2
            and word.isalpha()
            and word not in ENGLISH_STOPWORDS
        ]
        
        # 统计词频
        word_counts = Counter(filtered_words)
        
        # 返回前N个高频词
        return dict(word_counts.most_common(max_keywords))
        
    except Exception as e:
        logger.error(f"英文关键词提取失败: {e}")
        return {}


def extract_keywords_tfidf(text: str, max_keywords: int = 20, language: Language = None) -> Dict[str, int]:
    """使用TF-IDF提取关键词"""
    try:
        if language is None:
            language = detect_language(text)
        
        # 分句
        sentences = sent_tokenize(text) if language == Language.ENGLISH else re.split(r'[。！？]', text)
        sentences = [s.strip() for s in sentences if s.strip()]
        
        if len(sentences) < 2:
            # 如果句子太少，回退到简单词频统计
            if language == Language.CHINESE:
                return extract_keywords_chinese(text, max_keywords)
            else:
                return extract_keywords_english(text, max_keywords)
        
        # 使用TF-IDF
        if language == Language.CHINESE:
            # 中文需要先分词
            processed_sentences = []
            for sentence in sentences:
                words = jieba.cut(sentence)
                filtered_words = [
                    word for word in words
                    if len(word.strip()) > 1 
                    and word.strip() not in CHINESE_STOPWORDS
                ]
                processed_sentences.append(' '.join(filtered_words))
        else:
            processed_sentences = sentences
        
        # 创建TF-IDF向量化器
        vectorizer = TfidfVectorizer(
            max_features=max_keywords * 2,
            stop_words='english' if language == Language.ENGLISH else None,
            ngram_range=(1, 2),  # 包括单词和双词组合
        )
        
        # 计算TF-IDF
        tfidf_matrix = vectorizer.fit_transform(processed_sentences)
        feature_names = vectorizer.get_feature_names_out()
        
        # 计算每个词的平均TF-IDF分数
        mean_scores = tfidf_matrix.mean(axis=0).A1
        
        # 创建词汇-分数映射
        word_scores = dict(zip(feature_names, mean_scores))
        
        # 按分数排序并返回前N个
        sorted_words = sorted(word_scores.items(), key=lambda x: x[1], reverse=True)
        
        # 转换为词频格式（这里用分数代替真实频率）
        result = {}
        for word, score in sorted_words[:max_keywords]:
            # 将TF-IDF分数转换为整数（乘以100并取整）
            result[word] = max(1, int(score * 100))
        
        return result
        
    except Exception as e:
        logger.error(f"TF-IDF关键词提取失败: {e}")
        # 回退到简单方法
        if language == Language.CHINESE:
            return extract_keywords_chinese(text, max_keywords)
        else:
            return extract_keywords_english(text, max_keywords)


def extract_keywords(text: str, max_keywords: int = None, use_tfidf: bool = True) -> Dict[str, int]:
    """提取关键词"""
    if not text:
        return {}
    
    if max_keywords is None:
        max_keywords = config.max_keywords
    
    # 清理文本
    cleaned_text = clean_text(text)
    if not cleaned_text:
        return {}
    
    # 检测语言
    language = detect_language(cleaned_text)
    
    # 选择提取方法
    if use_tfidf and len(cleaned_text) > 500:  # 文本足够长时使用TF-IDF
        return extract_keywords_tfidf(cleaned_text, max_keywords, language)
    elif language == Language.CHINESE:
        return extract_keywords_chinese(cleaned_text, max_keywords)
    else:
        return extract_keywords_english(cleaned_text, max_keywords)


def generate_summary_simple(text: str, ratio: float = 0.01) -> str:
    """生成简单摘要（基于句子重要性）"""
    if not text:
        return ""
    
    # 检测语言
    language = detect_language(text)
    
    # 分句
    if language == Language.CHINESE:
        sentences = re.split(r'[。！？]', text)
    else:
        sentences = sent_tokenize(text)
    
    sentences = [s.strip() for s in sentences if s.strip()]
    
    if not sentences:
        return ""
    
    # 计算需要的句子数量
    target_sentences = max(1, int(len(sentences) * ratio))
    target_sentences = min(target_sentences, len(sentences))
    
    # 简单策略：选择前几句和最后几句
    if target_sentences >= len(sentences):
        return ' '.join(sentences)
    
    if target_sentences == 1:
        return sentences[0]
    
    # 选择开头和结尾的句子
    half = target_sentences // 2
    selected_sentences = sentences[:half] + sentences[-half:]
    
    return ' '.join(selected_sentences)


def generate_summary(text: str, min_ratio: float = None, max_length: int = None) -> str:
    """生成文本摘要"""
    if not text:
        return ""
    
    if min_ratio is None:
        min_ratio = config.min_summary_ratio
    
    # 计算目标长度
    word_count, _ = count_words(text)
    target_length = max(50, int(word_count * min_ratio))  # 至少50字
    
    if max_length:
        target_length = min(target_length, max_length)
    
    # 生成摘要
    summary = generate_summary_simple(text, min_ratio)
    
    # 如果摘要太长，截断
    if max_length and len(summary) > max_length:
        summary = summary[:max_length] + "..."
    
    return summary


def calculate_text_similarity(text1: str, text2: str) -> float:
    """计算两个文本的相似度"""
    try:
        if not text1 or not text2:
            return 0.0
        
        # 使用TF-IDF计算相似度
        vectorizer = TfidfVectorizer()
        tfidf_matrix = vectorizer.fit_transform([text1, text2])
        
        # 计算余弦相似度
        from sklearn.metrics.pairwise import cosine_similarity
        similarity = cosine_similarity(tfidf_matrix[0:1], tfidf_matrix[1:2])[0][0]
        
        return float(similarity)
        
    except Exception as e:
        logger.error(f"文本相似度计算失败: {e}")
        return 0.0
