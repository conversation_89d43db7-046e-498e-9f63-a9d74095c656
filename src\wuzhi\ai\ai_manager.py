"""
AI管理器
"""

import asyncio
from typing import Dict, List, Optional, Any
from pathlib import Path

from .base import BaseAIProvider, AIResponse, AITaskType
from .ollama_provider import OllamaProvider
from ..core.config import config
from ..core.logger import get_logger
from ..extractors.base import ExtractionResult

logger = get_logger(__name__)


class AIManager:
    """AI管理器"""
    
    def __init__(self):
        self.providers: Dict[str, BaseAIProvider] = {}
        self.default_provider: Optional[str] = None
        self.fallback_enabled = True
        
        # 初始化默认提供者
        self._init_default_providers()
    
    def _init_default_providers(self):
        """初始化默认AI提供者"""
        # 添加Ollama提供者
        if config.use_ai_summary:
            ollama = OllamaProvider()
            self.register_provider("ollama", ollama)
            self.default_provider = "ollama"
    
    def register_provider(self, name: str, provider: BaseAIProvider):
        """注册AI提供者"""
        self.providers[name] = provider
        logger.info(f"注册AI提供者: {name}")
    
    async def initialize_all(self) -> Dict[str, bool]:
        """初始化所有提供者"""
        results = {}
        
        for name, provider in self.providers.items():
            try:
                logger.info(f"初始化AI提供者: {name}")
                success = await provider.initialize()
                results[name] = success
                
                if success:
                    logger.info(f"AI提供者初始化成功: {name}")
                    if not self.default_provider:
                        self.default_provider = name
                else:
                    logger.warning(f"AI提供者初始化失败: {name}")
                    
            except Exception as e:
                logger.error(f"AI提供者初始化异常 {name}: {e}")
                results[name] = False
        
        return results
    
    async def get_available_provider(self, task_type: AITaskType = None) -> Optional[BaseAIProvider]:
        """获取可用的AI提供者"""
        # 优先使用默认提供者
        if self.default_provider and self.default_provider in self.providers:
            provider = self.providers[self.default_provider]
            if provider.is_available and (not task_type or provider.is_task_supported(task_type)):
                return provider
        
        # 查找其他可用提供者
        for provider in self.providers.values():
            if provider.is_available and (not task_type or provider.is_task_supported(task_type)):
                return provider
        
        return None
    
    async def generate_summary(self, text: str, max_length: int = 200, 
                             language: str = "zh", provider_name: str = None) -> AIResponse:
        """生成摘要"""
        if provider_name:
            provider = self.providers.get(provider_name)
            if not provider or not provider.is_available:
                return AIResponse(
                    success=False,
                    error_message=f"指定的AI提供者不可用: {provider_name}"
                )
        else:
            provider = await self.get_available_provider(AITaskType.SUMMARIZE)
            if not provider:
                return AIResponse(
                    success=False,
                    error_message="没有可用的AI提供者支持摘要生成"
                )
        
        try:
            logger.info(f"使用 {provider.name} 生成摘要")
            response = await provider.summarize_text(text, max_length, language)
            
            if response.success:
                logger.info(f"摘要生成成功，长度: {len(response.content)}")
            else:
                logger.warning(f"摘要生成失败: {response.error_message}")
            
            return response
            
        except Exception as e:
            logger.error(f"摘要生成异常: {e}")
            return AIResponse(
                success=False,
                error_message=f"摘要生成异常: {e}"
            )
    
    async def extract_metadata(self, text: str, document_type: str = None, 
                             provider_name: str = None) -> AIResponse:
        """提取元数据"""
        if provider_name:
            provider = self.providers.get(provider_name)
            if not provider or not provider.is_available:
                return AIResponse(
                    success=False,
                    error_message=f"指定的AI提供者不可用: {provider_name}"
                )
        else:
            provider = await self.get_available_provider(AITaskType.EXTRACT_METADATA)
            if not provider:
                return AIResponse(
                    success=False,
                    error_message="没有可用的AI提供者支持元数据提取"
                )
        
        try:
            logger.info(f"使用 {provider.name} 提取元数据")
            response = await provider.extract_metadata(text, document_type)
            
            if response.success:
                logger.info("元数据提取成功")
            else:
                logger.warning(f"元数据提取失败: {response.error_message}")
            
            return response
            
        except Exception as e:
            logger.error(f"元数据提取异常: {e}")
            return AIResponse(
                success=False,
                error_message=f"元数据提取异常: {e}"
            )
    
    async def classify_document(self, text: str, provider_name: str = None) -> AIResponse:
        """文档分类"""
        if provider_name:
            provider = self.providers.get(provider_name)
            if not provider or not provider.is_available:
                return AIResponse(
                    success=False,
                    error_message=f"指定的AI提供者不可用: {provider_name}"
                )
        else:
            provider = await self.get_available_provider(AITaskType.CLASSIFY_DOCUMENT)
            if not provider:
                return AIResponse(
                    success=False,
                    error_message="没有可用的AI提供者支持文档分类"
                )
        
        try:
            logger.info(f"使用 {provider.name} 进行文档分类")
            response = await provider.classify_document(text)
            
            if response.success:
                logger.info("文档分类成功")
            else:
                logger.warning(f"文档分类失败: {response.error_message}")
            
            return response
            
        except Exception as e:
            logger.error(f"文档分类异常: {e}")
            return AIResponse(
                success=False,
                error_message=f"文档分类异常: {e}"
            )
    
    async def extract_keywords(self, text: str, max_keywords: int = 10, 
                             provider_name: str = None) -> AIResponse:
        """提取关键词"""
        if provider_name:
            provider = self.providers.get(provider_name)
            if not provider or not provider.is_available:
                return AIResponse(
                    success=False,
                    error_message=f"指定的AI提供者不可用: {provider_name}"
                )
        else:
            provider = await self.get_available_provider(AITaskType.EXTRACT_KEYWORDS)
            if not provider:
                return AIResponse(
                    success=False,
                    error_message="没有可用的AI提供者支持关键词提取"
                )
        
        try:
            logger.info(f"使用 {provider.name} 提取关键词")
            response = await provider.extract_keywords(text, max_keywords)
            
            if response.success:
                logger.info(f"关键词提取成功")
            else:
                logger.warning(f"关键词提取失败: {response.error_message}")
            
            return response
            
        except Exception as e:
            logger.error(f"关键词提取异常: {e}")
            return AIResponse(
                success=False,
                error_message=f"关键词提取异常: {e}"
            )
    
    async def translate_to_chinese(self, text: str, provider_name: str = None) -> AIResponse:
        """翻译为中文"""
        if provider_name:
            provider = self.providers.get(provider_name)
            if not provider or not provider.is_available:
                return AIResponse(
                    success=False,
                    error_message=f"指定的AI提供者不可用: {provider_name}"
                )
        else:
            provider = await self.get_available_provider(AITaskType.TRANSLATE)
            if not provider:
                return AIResponse(
                    success=False,
                    error_message="没有可用的AI提供者支持翻译"
                )
        
        try:
            logger.info(f"使用 {provider.name} 翻译为中文")
            response = await provider.translate_text(text, "zh")
            
            if response.success:
                logger.info("翻译成功")
            else:
                logger.warning(f"翻译失败: {response.error_message}")
            
            return response
            
        except Exception as e:
            logger.error(f"翻译异常: {e}")
            return AIResponse(
                success=False,
                error_message=f"翻译异常: {e}"
            )
    
    async def enhance_analysis_with_ai(self, extraction_result: ExtractionResult) -> Dict[str, AIResponse]:
        """使用AI增强文档分析"""
        results = {}
        text = extraction_result.plain_text
        
        if not text or len(text.strip()) < 50:
            logger.warning("文本内容太短，跳过AI增强分析")
            return results
        
        # 限制文本长度以避免超出模型限制
        max_text_length = 4000  # 大约4000字符
        if len(text) > max_text_length:
            text = text[:max_text_length] + "..."
            logger.info(f"文本过长，截断到 {max_text_length} 字符")
        
        # 并发执行多个AI任务
        tasks = []
        
        # 生成摘要
        if config.use_ai_summary:
            tasks.append(("summary", self.generate_summary(text)))
        
        # 提取关键词
        tasks.append(("keywords", self.extract_keywords(text)))
        
        # 文档分类
        tasks.append(("classification", self.classify_document(text)))
        
        # 提取元数据
        tasks.append(("metadata", self.extract_metadata(text)))
        
        # 执行所有任务
        if tasks:
            logger.info(f"开始AI增强分析，任务数: {len(tasks)}")
            
            task_results = await asyncio.gather(
                *[task[1] for task in tasks],
                return_exceptions=True
            )
            
            for i, (task_name, _) in enumerate(tasks):
                result = task_results[i]
                if isinstance(result, Exception):
                    logger.error(f"AI任务 {task_name} 异常: {result}")
                    results[task_name] = AIResponse(
                        success=False,
                        error_message=str(result)
                    )
                else:
                    results[task_name] = result
        
        return results
    
    def get_provider_status(self) -> Dict[str, Dict[str, Any]]:
        """获取所有提供者状态"""
        status = {}
        
        for name, provider in self.providers.items():
            status[name] = {
                'available': provider.is_available,
                'info': provider.get_info(),
                'is_default': name == self.default_provider,
            }
        
        return status
    
    async def test_all_providers(self) -> Dict[str, Dict[str, Any]]:
        """测试所有提供者"""
        results = {}
        
        for name, provider in self.providers.items():
            if hasattr(provider, 'test_connection'):
                try:
                    test_result = await provider.test_connection()
                    results[name] = test_result
                except Exception as e:
                    results[name] = {
                        'error': str(e),
                        'service_available': False,
                        'model_available': False,
                        'test_generation': False,
                    }
            else:
                results[name] = {
                    'health_check': await provider.health_check(),
                    'available': provider.is_available,
                }
        
        return results
    
    def set_default_provider(self, provider_name: str):
        """设置默认提供者"""
        if provider_name in self.providers:
            self.default_provider = provider_name
            logger.info(f"设置默认AI提供者: {provider_name}")
        else:
            raise ValueError(f"提供者不存在: {provider_name}")
    
    def is_ai_available(self) -> bool:
        """检查是否有可用的AI提供者"""
        return any(provider.is_available for provider in self.providers.values())


# 全局AI管理器实例
ai_manager = AIManager()
