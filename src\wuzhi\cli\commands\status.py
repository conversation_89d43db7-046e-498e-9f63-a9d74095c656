"""
状态查看命令
"""

import click
import asyncio
from pathlib import Path

from ...core.config import config
from ...core.database import get_db_session, test_connection
from ...core.models import Document
from ...core.logger import get_logger

logger = get_logger(__name__)


@click.command('status')
@click.option('--detailed', '-d', is_flag=True, help='显示详细信息')
@click.option('--ai', is_flag=True, help='显示AI状态')
@click.pass_context
def status_command(ctx, detailed, ai):
    """显示系统状态"""
    try:
        click.echo("悟知 (Wu<PERSON>hi) 系统状态")
        click.echo("=" * 40)
        
        # 基本信息
        click.echo(f"版本: {config.version}")
        click.echo(f"配置目录: {config.config_dir}")
        click.echo(f"数据目录: {config.data_dir}")
        
        # 数据库状态
        click.echo("\n数据库状态:")
        click.echo("-" * 20)
        
        if test_connection():
            click.echo("✓ 数据库连接正常")
            
            # 获取数据库统计
            with get_db_session() as session:
                total_docs = session.query(Document).count()
                analyzed_docs = session.query(Document).filter(Document.is_analyzed == True).count()
                duplicate_docs = session.query(Document).filter(Document.is_duplicate == True).count()
                
                click.echo(f"总文档数: {total_docs}")
                click.echo(f"已分析: {analyzed_docs}")
                click.echo(f"未分析: {total_docs - analyzed_docs}")
                click.echo(f"重复文档: {duplicate_docs}")
                
                if total_docs > 0:
                    analysis_rate = (analyzed_docs / total_docs) * 100
                    duplicate_rate = (duplicate_docs / total_docs) * 100
                    click.echo(f"分析率: {analysis_rate:.1f}%")
                    click.echo(f"重复率: {duplicate_rate:.1f}%")
        else:
            click.echo("✗ 数据库连接失败")
        
        # 详细信息
        if detailed:
            click.echo("\n详细信息:")
            click.echo("-" * 20)
            
            # 文件系统信息
            db_size = config.database_path.stat().st_size if config.database_path.exists() else 0
            click.echo(f"数据库大小: {_format_size(db_size)}")
            
            # 缓存信息
            cache_dir = config.get_cache_dir()
            if cache_dir.exists():
                cache_size = sum(f.stat().st_size for f in cache_dir.rglob('*') if f.is_file())
                click.echo(f"缓存大小: {_format_size(cache_size)}")
            
            # 日志信息
            if config.log_file and config.log_file.exists():
                log_size = config.log_file.stat().st_size
                click.echo(f"日志大小: {_format_size(log_size)}")
            
            # 文档类型分布
            _show_document_type_distribution()
        
        # AI状态
        if ai:
            click.echo("\nAI状态:")
            click.echo("-" * 20)
            
            async def show_ai_status():
                from ...ai.ai_manager import ai_manager
                
                # 初始化AI管理器
                init_results = await ai_manager.initialize_all()
                
                if not init_results:
                    click.echo("✗ 没有配置AI提供者")
                    return
                
                # 显示提供者状态
                status = ai_manager.get_provider_status()
                
                for name, info in status.items():
                    status_text = "✓ 可用" if info['available'] else "✗ 不可用"
                    default_text = " (默认)" if info['is_default'] else ""
                    click.echo(f"{name}: {status_text}{default_text}")
                
                # 测试AI功能
                if detailed and ai_manager.is_ai_available():
                    click.echo("\n测试AI功能...")
                    test_results = await ai_manager.test_all_providers()
                    
                    for provider, result in test_results.items():
                        if result.get('service_available'):
                            if result.get('test_generation'):
                                click.echo(f"  ✓ {provider}: 文本生成正常")
                            else:
                                click.echo(f"  ✗ {provider}: 文本生成失败")
                        else:
                            click.echo(f"  ✗ {provider}: 服务不可用")
            
            asyncio.run(show_ai_status())
        
        # 系统健康检查
        click.echo("\n系统健康检查:")
        click.echo("-" * 20)
        
        health_score = 0
        total_checks = 4
        
        # 检查数据库
        if test_connection():
            click.echo("✓ 数据库连接")
            health_score += 1
        else:
            click.echo("✗ 数据库连接")
        
        # 检查数据目录
        if config.data_dir.exists() and config.data_dir.is_dir():
            click.echo("✓ 数据目录")
            health_score += 1
        else:
            click.echo("✗ 数据目录")
        
        # 检查配置
        if config.config_dir.exists():
            click.echo("✓ 配置目录")
            health_score += 1
        else:
            click.echo("✗ 配置目录")
        
        # 检查日志
        if config.log_file and config.log_file.parent.exists():
            click.echo("✓ 日志目录")
            health_score += 1
        else:
            click.echo("✗ 日志目录")
        
        # 显示健康评分
        health_percentage = (health_score / total_checks) * 100
        click.echo(f"\n系统健康度: {health_percentage:.0f}% ({health_score}/{total_checks})")
        
        if health_percentage < 100:
            click.echo("\n建议:")
            if health_score == 0:
                click.echo("- 运行 'wuzhi init' 初始化系统")
            else:
                click.echo("- 检查配置文件和目录权限")
                click.echo("- 查看日志文件获取详细错误信息")
        
    except Exception as e:
        click.echo(f"获取状态信息失败: {e}", err=True)
        logger.error(f"获取状态信息失败: {e}")


def _format_size(size_bytes):
    """格式化文件大小"""
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f} {size_names[i]}"


def _show_document_type_distribution():
    """显示文档类型分布"""
    try:
        with get_db_session() as session:
            # 获取文档类型统计
            from sqlalchemy import func
            
            type_stats = session.query(
                Document.document_type,
                func.count(Document.id).label('count')
            ).filter(
                Document.document_type.isnot(None)
            ).group_by(
                Document.document_type
            ).all()
            
            if type_stats:
                click.echo("\n文档类型分布:")
                for doc_type, count in type_stats:
                    click.echo(f"  {doc_type}: {count}")
    
    except Exception as e:
        logger.debug(f"获取文档类型分布失败: {e}")


@click.command('health')
@click.pass_context
def health_command(ctx):
    """系统健康检查"""
    try:
        click.echo("执行系统健康检查...")
        
        issues = []
        
        # 检查数据库
        if not test_connection():
            issues.append("数据库连接失败")
        
        # 检查必要目录
        required_dirs = [
            config.data_dir,
            config.config_dir,
        ]
        
        for directory in required_dirs:
            if not directory.exists():
                issues.append(f"目录不存在: {directory}")
            elif not directory.is_dir():
                issues.append(f"路径不是目录: {directory}")
        
        # 检查数据库文件
        if not config.database_path.exists():
            issues.append("数据库文件不存在")
        
        # 检查权限
        try:
            test_file = config.data_dir / "test_write"
            test_file.write_text("test")
            test_file.unlink()
        except Exception:
            issues.append("数据目录没有写权限")
        
        # 显示结果
        if not issues:
            click.echo("✓ 系统健康检查通过")
        else:
            click.echo("✗ 发现以下问题:")
            for issue in issues:
                click.echo(f"  - {issue}")
            
            click.echo("\n建议解决方案:")
            click.echo("1. 运行 'wuzhi init' 重新初始化系统")
            click.echo("2. 检查目录权限")
            click.echo("3. 查看日志文件获取详细信息")
    
    except Exception as e:
        click.echo(f"健康检查失败: {e}", err=True)
        logger.error(f"健康检查失败: {e}")


# 将health命令添加到status组
status_command.add_command(health_command)
