"""
内容提取器工厂
"""

from pathlib import Path
from typing import Dict, List, Optional
from ..core.models import FileType
from ..core.logger import get_logger
from ..core.file_detector import file_detector

from .base import BaseExtractor, ExtractionResult
from .text_extractor import TextExtractor
from .pdf_extractor import PDFExtractor
from .docx_extractor import DOCXExtractor
from .epub_extractor import EPUBExtractor
from .markdown_extractor import MarkdownExtractor

logger = get_logger(__name__)


class ExtractorFactory:
    """内容提取器工厂"""
    
    def __init__(self):
        self._extractors: Dict[FileType, BaseExtractor] = {}
        self._register_default_extractors()
    
    def _register_default_extractors(self):
        """注册默认提取器"""
        extractors = [
            TextExtractor(),
            PDFExtractor(),
            DOCXExtractor(),
            EPUBExtractor(),
            MarkdownExtractor(),
        ]
        
        for extractor in extractors:
            for file_type in extractor.get_supported_types():
                self._extractors[file_type] = extractor
                logger.debug(f"注册提取器: {file_type.value} -> {extractor.name}")
    
    def register_extractor(self, file_type: FileType, extractor: BaseExtractor):
        """注册自定义提取器"""
        self._extractors[file_type] = extractor
        logger.info(f"注册自定义提取器: {file_type.value} -> {extractor.name}")
    
    def get_extractor(self, file_path: Path, file_type: FileType = None) -> Optional[BaseExtractor]:
        """获取适合的提取器"""
        if file_type is None:
            file_type = file_detector.detect_file_type(file_path)
        
        extractor = self._extractors.get(file_type)
        
        if extractor and extractor.can_extract(file_path, file_type):
            return extractor
        
        logger.warning(f"未找到适合的提取器: {file_path} ({file_type.value})")
        return None
    
    def extract(self, file_path: Path, file_type: FileType = None) -> ExtractionResult:
        """提取文档内容"""
        try:
            if not file_path.exists():
                return ExtractionResult(
                    success=False,
                    error_message="文件不存在"
                )
            
            # 获取提取器
            extractor = self.get_extractor(file_path, file_type)
            if not extractor:
                return ExtractionResult(
                    success=False,
                    error_message=f"不支持的文件类型: {file_type.value if file_type else 'unknown'}"
                )
            
            # 执行提取
            logger.info(f"使用 {extractor.name} 提取: {file_path}")
            result = extractor.extract(file_path)
            
            if result.success:
                logger.info(f"提取成功: {file_path}, 文本长度: {len(result.plain_text)}")
            else:
                logger.warning(f"提取失败: {file_path}, 错误: {result.error_message}")
            
            return result
            
        except Exception as e:
            logger.error(f"提取过程异常 {file_path}: {e}")
            return ExtractionResult(
                success=False,
                error_message=f"提取过程异常: {e}"
            )
    
    def get_supported_types(self) -> List[FileType]:
        """获取所有支持的文件类型"""
        return list(self._extractors.keys())
    
    def get_extractor_info(self) -> Dict[str, List[str]]:
        """获取提取器信息"""
        info = {}
        for file_type, extractor in self._extractors.items():
            extractor_name = extractor.name
            if extractor_name not in info:
                info[extractor_name] = []
            info[extractor_name].append(file_type.value)
        
        return info
    
    def batch_extract(self, file_paths: List[Path]) -> Dict[Path, ExtractionResult]:
        """批量提取文档内容"""
        results = {}
        
        for file_path in file_paths:
            try:
                result = self.extract(file_path)
                results[file_path] = result
            except Exception as e:
                logger.error(f"批量提取失败 {file_path}: {e}")
                results[file_path] = ExtractionResult(
                    success=False,
                    error_message=f"批量提取失败: {e}"
                )
        
        return results
    
    def extract_with_fallback(self, file_path: Path) -> ExtractionResult:
        """带回退机制的提取"""
        # 首先尝试自动检测文件类型
        detected_type = file_detector.detect_file_type(file_path)
        result = self.extract(file_path, detected_type)
        
        if result.success:
            return result
        
        # 如果失败，尝试其他可能的提取器
        logger.info(f"主提取器失败，尝试回退方案: {file_path}")
        
        # 尝试文本提取器（通用回退）
        if detected_type != FileType.TXT:
            text_result = self.extract(file_path, FileType.TXT)
            if text_result.success and len(text_result.plain_text.strip()) > 50:
                logger.info(f"文本提取器回退成功: {file_path}")
                return text_result
        
        # 返回原始失败结果
        return result


# 全局提取器工厂实例
extractor_factory = ExtractorFactory()


class ContentExtractor:
    """内容提取器包装类，提供简化的接口"""
    
    def __init__(self):
        self.factory = extractor_factory
    
    def extract_file(self, file_path: str | Path) -> ExtractionResult:
        """提取单个文件"""
        if isinstance(file_path, str):
            file_path = Path(file_path)
        
        return self.factory.extract_with_fallback(file_path)
    
    def extract_files(self, file_paths: List[str | Path]) -> Dict[Path, ExtractionResult]:
        """提取多个文件"""
        paths = []
        for path in file_paths:
            if isinstance(path, str):
                path = Path(path)
            paths.append(path)
        
        return self.factory.batch_extract(paths)
    
    def is_supported(self, file_path: str | Path) -> bool:
        """检查文件是否支持提取"""
        if isinstance(file_path, str):
            file_path = Path(file_path)
        
        if not file_path.exists():
            return False
        
        file_type = file_detector.detect_file_type(file_path)
        return file_type in self.factory.get_supported_types()
    
    def get_supported_extensions(self) -> List[str]:
        """获取支持的文件扩展名"""
        extensions = []
        for file_type in self.factory.get_supported_types():
            # 根据文件类型映射扩展名
            type_to_ext = {
                FileType.TXT: ['.txt'],
                FileType.PDF: ['.pdf'],
                FileType.DOCX: ['.docx'],
                FileType.DOC: ['.doc'],
                FileType.EPUB: ['.epub'],
                FileType.MD: ['.md', '.markdown'],
                FileType.PPT: ['.ppt'],
                FileType.PPTX: ['.pptx'],
                FileType.WPS: ['.wps'],
                FileType.CEB: ['.ceb'],
            }
            
            if file_type in type_to_ext:
                extensions.extend(type_to_ext[file_type])
        
        return sorted(list(set(extensions)))


# 全局内容提取器实例
content_extractor = ContentExtractor()
