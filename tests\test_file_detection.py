"""
文件类型检测测试
"""

import pytest
import tempfile
from pathlib import Path

from src.wuzhi.core.file_detector import FileTypeDetector
from src.wuzhi.core.models import FileType


class TestFileTypeDetector:
    """文件类型检测器测试"""
    
    def setup_method(self):
        """设置测试环境"""
        self.detector = FileTypeDetector()
    
    def test_text_file_detection(self):
        """测试文本文件检测"""
        # 创建普通文本文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write("This is a test text file.\n这是一个测试文本文件。")
            temp_path = Path(f.name)
        
        try:
            file_type = self.detector.detect_file_type(temp_path)
            assert file_type == FileType.TXT
        finally:
            temp_path.unlink()
    
    def test_markdown_file_detection(self):
        """测试Markdown文件检测"""
        markdown_content = """# 标题
        
## 二级标题

这是一个**粗体**文本和*斜体*文本。

- 列表项1
- 列表项2

```python
print("Hello, World!")
```

[链接](https://example.com)
"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False) as f:
            f.write(markdown_content)
            temp_path = Path(f.name)
        
        try:
            file_type = self.detector.detect_file_type(temp_path)
            assert file_type == FileType.MD
        finally:
            temp_path.unlink()
    
    def test_pdf_signature_detection(self):
        """测试PDF文件头检测"""
        # 创建一个带有PDF文件头的文件
        pdf_header = b'%PDF-1.4\n%\xe2\xe3\xcf\xd3\n'
        
        with tempfile.NamedTemporaryFile(delete=False) as f:
            f.write(pdf_header)
            f.write(b'fake pdf content')
            temp_path = Path(f.name)
        
        try:
            file_type = self.detector.detect_file_type(temp_path)
            assert file_type == FileType.PDF
        finally:
            temp_path.unlink()
    
    def test_html_content_detection(self):
        """测试HTML内容检测"""
        html_content = """<!DOCTYPE html>
<html>
<head>
    <title>Test HTML</title>
</head>
<body>
    <h1>Hello, World!</h1>
    <p>This is a test HTML file.</p>
</body>
</html>"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False) as f:
            f.write(html_content)
            temp_path = Path(f.name)
        
        try:
            file_type = self.detector.detect_file_type(temp_path)
            assert file_type == FileType.TXT  # HTML被归类为文本文件
        finally:
            temp_path.unlink()
    
    def test_utf8_bom_detection(self):
        """测试UTF-8 BOM检测"""
        # 创建带有UTF-8 BOM的文件
        content = "这是一个带有BOM的UTF-8文件"
        bom_content = '\ufeff' + content  # UTF-8 BOM
        
        with tempfile.NamedTemporaryFile(mode='w', encoding='utf-8-sig', delete=False) as f:
            f.write(content)
            temp_path = Path(f.name)
        
        try:
            file_type = self.detector.detect_file_type(temp_path)
            assert file_type == FileType.TXT
        finally:
            temp_path.unlink()
    
    def test_unknown_file_detection(self):
        """测试未知文件类型检测"""
        # 创建一个二进制文件，没有明确的文件头
        binary_content = bytes(range(256))
        
        with tempfile.NamedTemporaryFile(delete=False) as f:
            f.write(binary_content)
            temp_path = Path(f.name)
        
        try:
            file_type = self.detector.detect_file_type(temp_path)
            assert file_type == FileType.UNKNOWN
        finally:
            temp_path.unlink()
    
    def test_extension_fallback(self):
        """测试扩展名回退检测"""
        # 创建一个没有明确文件头但有扩展名的文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False) as f:
            f.write("Simple text without markdown features")
            temp_path = Path(f.name)
        
        try:
            file_type = self.detector.detect_file_type(temp_path)
            # 应该根据扩展名识别为Markdown
            assert file_type == FileType.MD
        finally:
            temp_path.unlink()
    
    def test_is_supported_file(self):
        """测试支持的文件类型检查"""
        # 创建支持的文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write("test content")
            temp_path = Path(f.name)
        
        try:
            assert self.detector.is_supported_file(temp_path) == True
        finally:
            temp_path.unlink()
        
        # 测试不存在的文件
        non_existent = Path("non_existent_file.txt")
        assert self.detector.is_supported_file(non_existent) == False
    
    def test_get_file_info(self):
        """测试获取文件信息"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write("test content for file info")
            temp_path = Path(f.name)
        
        try:
            info = self.detector.get_file_info(temp_path)
            
            assert 'path' in info
            assert 'name' in info
            assert 'size' in info
            assert 'file_type' in info
            assert 'is_supported' in info
            
            assert info['file_type'] == FileType.TXT
            assert info['is_supported'] == True
            assert info['size'] > 0
            
        finally:
            temp_path.unlink()
    
    def test_cache_functionality(self):
        """测试缓存功能"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write("test content for cache")
            temp_path = Path(f.name)
        
        try:
            # 第一次检测
            file_type1 = self.detector.detect_file_type(temp_path)
            
            # 检查缓存中是否有结果
            assert str(temp_path) in self.detector.detection_cache
            
            # 第二次检测应该使用缓存
            file_type2 = self.detector.detect_file_type(temp_path)
            
            assert file_type1 == file_type2
            
            # 清空缓存
            self.detector.clear_cache()
            assert len(self.detector.detection_cache) == 0
            
        finally:
            temp_path.unlink()
    
    def test_markdown_content_detection(self):
        """测试Markdown内容特征检测"""
        # 测试各种Markdown特征
        markdown_features = [
            "# 这是标题",
            "## 二级标题",
            "- 列表项",
            "* 另一个列表",
            "**粗体文本**",
            "*斜体文本*",
            "[链接](http://example.com)",
            "```代码块```",
        ]
        
        for feature in markdown_features:
            with tempfile.NamedTemporaryFile(mode='w', delete=False) as f:
                f.write(feature)
                temp_path = Path(f.name)
            
            try:
                # 通过内容检测应该识别为文本或Markdown特征
                file_type = self.detector._detect_by_content(temp_path)
                # 内容检测可能返回TXT或MD，都是可接受的
                assert file_type in [FileType.TXT, FileType.MD, FileType.UNKNOWN]
            finally:
                temp_path.unlink()


if __name__ == "__main__":
    pytest.main([__file__])
