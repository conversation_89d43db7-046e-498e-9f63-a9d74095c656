version: '3.8'

services:
  wuzhi:
    build: .
    container_name: wuzhi-app
    ports:
      - "8080:8080"
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./config:/app/config
    environment:
      - APP_NAME=悟知 (WuZhi)
      - DEBUG=false
      - LOG_LEVEL=INFO
      - DATABASE_URL=sqlite:///./data/wuzhi.db
      - OLLAMA_BASE_URL=http://ollama:11434
    depends_on:
      - ollama
    restart: unless-stopped
    networks:
      - wuzhi-network

  ollama:
    image: ollama/ollama:latest
    container_name: wuzhi-ollama
    ports:
      - "11434:11434"
    volumes:
      - ollama-data:/root/.ollama
    environment:
      - OLLAMA_HOST=0.0.0.0
    restart: unless-stopped
    networks:
      - wuzhi-network
    # 如果有GPU，取消注释以下行
    # deploy:
    #   resources:
    #     reservations:
    #       devices:
    #         - driver: nvidia
    #           count: 1
    #           capabilities: [gpu]

  # 可选：添加数据库服务（如果不使用SQLite）
  # postgres:
  #   image: postgres:15
  #   container_name: wuzhi-postgres
  #   environment:
  #     POSTGRES_DB: wuzhi
  #     POSTGRES_USER: wuzhi
  #     POSTGRES_PASSWORD: wuzhi_password
  #   volumes:
  #     - postgres-data:/var/lib/postgresql/data
  #   ports:
  #     - "5432:5432"
  #   restart: unless-stopped
  #   networks:
  #     - wuzhi-network

  # 可选：添加Redis缓存
  # redis:
  #   image: redis:7-alpine
  #   container_name: wuzhi-redis
  #   ports:
  #     - "6379:6379"
  #   volumes:
  #     - redis-data:/data
  #   restart: unless-stopped
  #   networks:
  #     - wuzhi-network

volumes:
  ollama-data:
  # postgres-data:
  # redis-data:

networks:
  wuzhi-network:
    driver: bridge
