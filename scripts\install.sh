#!/bin/bash
# 悟知 (<PERSON><PERSON><PERSON>) 安装脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查系统要求
check_requirements() {
    log_info "检查系统要求..."
    
    # 检查Python版本
    if command -v python3 &> /dev/null; then
        PYTHON_VERSION=$(python3 -c 'import sys; print(".".join(map(str, sys.version_info[:2])))')
        log_info "Python版本: $PYTHON_VERSION"
        
        # 检查Python版本是否满足要求
        if python3 -c 'import sys; exit(0 if sys.version_info >= (3, 9) else 1)'; then
            log_success "Python版本满足要求 (>= 3.9)"
        else
            log_error "Python版本不满足要求，需要Python 3.9或更高版本"
            exit 1
        fi
    else
        log_error "未找到Python3，请先安装Python 3.9或更高版本"
        exit 1
    fi
    
    # 检查Poetry
    if command -v poetry &> /dev/null; then
        POETRY_VERSION=$(poetry --version | cut -d' ' -f3)
        log_success "Poetry已安装，版本: $POETRY_VERSION"
    else
        log_warning "Poetry未安装，将自动安装..."
        install_poetry
    fi
}

# 安装Poetry
install_poetry() {
    log_info "安装Poetry..."
    curl -sSL https://install.python-poetry.org | python3 -
    
    # 添加Poetry到PATH
    export PATH="$HOME/.local/bin:$PATH"
    
    if command -v poetry &> /dev/null; then
        log_success "Poetry安装成功"
    else
        log_error "Poetry安装失败"
        exit 1
    fi
}

# 安装依赖
install_dependencies() {
    log_info "安装项目依赖..."
    
    # 配置Poetry
    poetry config virtualenvs.create true
    poetry config virtualenvs.in-project true
    
    # 安装依赖
    poetry install
    
    log_success "依赖安装完成"
}

# 初始化配置
init_config() {
    log_info "初始化配置..."
    
    # 创建必要的目录
    mkdir -p data logs config cache temp
    
    # 复制配置文件
    if [ ! -f .env ]; then
        cp .env.example .env
        log_success "配置文件已创建: .env"
        log_warning "请根据需要修改 .env 文件中的配置"
    else
        log_info "配置文件已存在: .env"
    fi
    
    log_success "配置初始化完成"
}

# 初始化数据库
init_database() {
    log_info "初始化数据库..."
    
    poetry run wuzhi init --force
    
    if [ $? -eq 0 ]; then
        log_success "数据库初始化完成"
    else
        log_error "数据库初始化失败"
        exit 1
    fi
}

# 运行测试
run_tests() {
    log_info "运行测试..."
    
    poetry run pytest tests/ -v
    
    if [ $? -eq 0 ]; then
        log_success "所有测试通过"
    else
        log_warning "部分测试失败，但不影响安装"
    fi
}

# 创建启动脚本
create_launcher() {
    log_info "创建启动脚本..."
    
    # 创建GUI启动脚本
    cat > wuzhi-gui.sh << 'EOF'
#!/bin/bash
cd "$(dirname "$0")"
poetry run wuzhi gui
EOF
    chmod +x wuzhi-gui.sh
    
    # 创建CLI启动脚本
    cat > wuzhi-cli.sh << 'EOF'
#!/bin/bash
cd "$(dirname "$0")"
poetry run wuzhi "$@"
EOF
    chmod +x wuzhi-cli.sh
    
    log_success "启动脚本已创建"
}

# 显示安装完成信息
show_completion_info() {
    echo
    log_success "悟知 (WuZhi) 安装完成！"
    echo
    echo "使用方法："
    echo "  启动图形界面: ./wuzhi-gui.sh"
    echo "  使用命令行: ./wuzhi-cli.sh --help"
    echo "  直接使用: poetry run wuzhi --help"
    echo
    echo "配置文件: .env"
    echo "数据目录: ./data"
    echo "日志目录: ./logs"
    echo
    echo "更多信息请查看 README.md"
}

# 主函数
main() {
    echo "========================================"
    echo "    悟知 (WuZhi) 安装程序"
    echo "========================================"
    echo
    
    check_requirements
    install_dependencies
    init_config
    init_database
    
    # 可选：运行测试
    if [ "$1" = "--with-tests" ]; then
        run_tests
    fi
    
    create_launcher
    show_completion_info
}

# 运行主函数
main "$@"
