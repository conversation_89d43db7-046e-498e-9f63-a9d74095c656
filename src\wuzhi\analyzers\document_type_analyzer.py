"""
文档类型分析器
"""

import re
from pathlib import Path
from typing import Dict, List, Tuple

from .base import BaseAnalyzer, AnalysisResult
from ..core.models import DocumentType, FileType
from ..core.logger import get_logger
from ..extractors.base import ExtractionResult

logger = get_logger(__name__)


class DocumentTypeAnalyzer(BaseAnalyzer):
    """文档类型分析器"""
    
    def __init__(self):
        super().__init__()
        self._init_type_patterns()
    
    def _init_type_patterns(self):
        """初始化文档类型识别模式"""
        self.type_patterns = {
            DocumentType.BOOK: {
                'keywords': [
                    '第一章', '第二章', '第三章', '章节', '目录', '前言', '序言', '后记',
                    'chapter', 'preface', 'introduction', 'conclusion', 'bibliography',
                    '出版社', '版权', 'ISBN', '印刷', '版次'
                ],
                'structure_patterns': [
                    r'第[一二三四五六七八九十\d]+章',
                    r'Chapter\s+\d+',
                    r'目\s*录',
                    r'Table\s+of\s+Contents'
                ],
                'file_indicators': ['.epub'],
                'weight': 1.0
            },
            
            DocumentType.PAPER: {
                'keywords': [
                    '摘要', '关键词', '引言', '结论', '参考文献', '致谢',
                    'abstract', 'keywords', 'introduction', 'conclusion', 'references',
                    'methodology', 'experiment', 'results', 'discussion',
                    '研究', '实验', '方法', '数据', '分析', '假设'
                ],
                'structure_patterns': [
                    r'摘\s*要',
                    r'Abstract',
                    r'关键词[：:]',
                    r'Keywords[：:]',
                    r'参考文献',
                    r'References',
                    r'\d+\.\s*引言',
                    r'\d+\.\s*Introduction'
                ],
                'file_indicators': ['.pdf'],
                'weight': 1.2
            },
            
            DocumentType.REPORT: {
                'keywords': [
                    '报告', '总结', '汇报', '分析报告', '调研报告', '工作报告',
                    'report', 'summary', 'analysis', 'survey', 'investigation',
                    '数据', '统计', '图表', '建议', '结论'
                ],
                'structure_patterns': [
                    r'执行摘要',
                    r'Executive\s+Summary',
                    r'报告摘要',
                    r'主要发现',
                    r'Key\s+Findings',
                    r'建议',
                    r'Recommendations'
                ],
                'file_indicators': ['.docx', '.doc', '.pdf'],
                'weight': 1.0
            },
            
            DocumentType.SUMMARY: {
                'keywords': [
                    '总结', '小结', '概要', '要点', '梳理', '回顾',
                    'summary', 'overview', 'recap', 'digest', 'synopsis',
                    '主要内容', '核心观点', '重点'
                ],
                'structure_patterns': [
                    r'总\s*结',
                    r'Summary',
                    r'概\s*要',
                    r'Overview',
                    r'要\s*点',
                    r'Key\s+Points'
                ],
                'file_indicators': ['.txt', '.md', '.docx'],
                'weight': 0.8
            },
            
            DocumentType.ARTICLE: {
                'keywords': [
                    '文章', '专栏', '评论', '观点', '见解', '思考',
                    'article', 'column', 'opinion', 'perspective', 'insight',
                    '作者', '发表', '刊登'
                ],
                'structure_patterns': [
                    r'作者[：:]',
                    r'Author[：:]',
                    r'发表于',
                    r'Published\s+in',
                    r'来源[：:]',
                    r'Source[：:]'
                ],
                'file_indicators': ['.txt', '.md', '.html'],
                'weight': 0.9
            },
            
            DocumentType.MANUAL: {
                'keywords': [
                    '手册', '指南', '说明书', '教程', '操作指南', '用户手册',
                    'manual', 'guide', 'handbook', 'tutorial', 'instructions',
                    '步骤', '操作', '配置', '安装', '使用方法'
                ],
                'structure_patterns': [
                    r'操作步骤',
                    r'Installation\s+Guide',
                    r'用户手册',
                    r'User\s+Manual',
                    r'快速入门',
                    r'Quick\s+Start',
                    r'第\d+步',
                    r'Step\s+\d+'
                ],
                'file_indicators': ['.pdf', '.docx', '.md'],
                'weight': 1.1
            },
            
            DocumentType.PRESENTATION: {
                'keywords': [
                    '演示', '幻灯片', '展示', '汇报', 'PPT',
                    'presentation', 'slides', 'slideshow',
                    '第一页', '下一页', '总结页'
                ],
                'structure_patterns': [
                    r'幻灯片\s*\d+',
                    r'Slide\s+\d+',
                    r'第\d+页',
                    r'Page\s+\d+'
                ],
                'file_indicators': ['.pptx', '.ppt'],
                'weight': 1.3
            }
        }
    
    def analyze(self, extraction_result: ExtractionResult, file_path: Path = None) -> AnalysisResult:
        """分析文档类型"""
        if not self.can_analyze(extraction_result):
            return self._create_error_result("无法分析文档类型：内容为空或提取失败")
        
        try:
            text = extraction_result.plain_text
            file_type = self._get_file_type(file_path) if file_path else None
            
            # 计算各文档类型的得分
            type_scores = self._calculate_type_scores(text, extraction_result, file_type)
            
            # 选择最高得分的类型
            if not type_scores:
                return self._create_error_result("无法确定文档类型")
            
            best_type = max(type_scores.items(), key=lambda x: x[1])
            document_type, confidence = best_type
            
            # 如果置信度太低，返回未知类型
            if confidence < 0.3:
                document_type = DocumentType.OTHER
                confidence = 0.5
            
            return self._create_success_result(
                document_type=document_type,
                document_type_confidence=confidence,
                confidence=confidence,
                metadata={
                    'type_scores': type_scores,
                    'analysis_method': 'pattern_matching',
                    'file_type': file_type.value if file_type else None
                }
            )
            
        except Exception as e:
            logger.error(f"文档类型分析失败: {e}")
            return self._create_error_result(f"分析失败: {e}")
    
    def _calculate_type_scores(self, text: str, extraction_result: ExtractionResult, file_type: FileType = None) -> Dict[DocumentType, float]:
        """计算各文档类型的得分"""
        scores = {}
        text_lower = text.lower()
        
        for doc_type, patterns in self.type_patterns.items():
            score = 0.0
            
            # 关键词匹配得分
            keyword_score = self._calculate_keyword_score(text_lower, patterns['keywords'])
            score += keyword_score * 0.4
            
            # 结构模式匹配得分
            structure_score = self._calculate_structure_score(text, patterns['structure_patterns'])
            score += structure_score * 0.3
            
            # 文件类型指示得分
            file_score = self._calculate_file_type_score(file_type, patterns['file_indicators'])
            score += file_score * 0.2
            
            # 文档结构特征得分
            feature_score = self._calculate_feature_score(extraction_result, doc_type)
            score += feature_score * 0.1
            
            # 应用权重
            score *= patterns['weight']
            
            scores[doc_type] = min(score, 1.0)
        
        return scores
    
    def _calculate_keyword_score(self, text: str, keywords: List[str]) -> float:
        """计算关键词匹配得分"""
        if not keywords:
            return 0.0
        
        matches = 0
        total_keywords = len(keywords)
        
        for keyword in keywords:
            if keyword.lower() in text:
                matches += 1
        
        return matches / total_keywords
    
    def _calculate_structure_score(self, text: str, patterns: List[str]) -> float:
        """计算结构模式匹配得分"""
        if not patterns:
            return 0.0
        
        matches = 0
        total_patterns = len(patterns)
        
        for pattern in patterns:
            if re.search(pattern, text, re.IGNORECASE):
                matches += 1
        
        return matches / total_patterns
    
    def _calculate_file_type_score(self, file_type: FileType, indicators: List[str]) -> float:
        """计算文件类型指示得分"""
        if not file_type or not indicators:
            return 0.0
        
        file_ext = f".{file_type.value}"
        return 1.0 if file_ext in indicators else 0.0
    
    def _calculate_feature_score(self, extraction_result: ExtractionResult, doc_type: DocumentType) -> float:
        """计算文档特征得分"""
        score = 0.0
        
        # 根据文档类型检查特定特征
        if doc_type == DocumentType.BOOK:
            # 书籍通常有多个章节标题
            if len(extraction_result.headings) > 5:
                score += 0.5
            # 书籍通常较长
            if extraction_result.word_count and extraction_result.word_count > 10000:
                score += 0.3
        
        elif doc_type == DocumentType.PAPER:
            # 论文通常有特定的结构
            if extraction_result.headings:
                paper_sections = ['abstract', 'introduction', 'method', 'result', 'conclusion', 'reference']
                heading_text = ' '.join(extraction_result.headings).lower()
                section_matches = sum(1 for section in paper_sections if section in heading_text)
                score += (section_matches / len(paper_sections)) * 0.6
        
        elif doc_type == DocumentType.PRESENTATION:
            # 演示文稿通常有页面信息
            if extraction_result.page_count and extraction_result.page_count > 1:
                score += 0.4
        
        elif doc_type == DocumentType.MANUAL:
            # 手册通常有步骤或编号
            if extraction_result.headings:
                numbered_headings = sum(1 for h in extraction_result.headings if re.match(r'^\d+\.', h.strip()))
                if numbered_headings > 0:
                    score += 0.5
        
        return min(score, 1.0)
    
    def _get_file_type(self, file_path: Path) -> FileType:
        """获取文件类型"""
        if not file_path:
            return None
        
        from ..core.file_detector import file_detector
        return file_detector.detect_file_type(file_path)
    
    def get_type_explanation(self, document_type: DocumentType, scores: Dict[DocumentType, float]) -> str:
        """获取类型判断的解释"""
        if document_type not in scores:
            return "无法确定文档类型"
        
        score = scores[document_type]
        
        explanations = {
            DocumentType.BOOK: f"检测到书籍特征，置信度: {score:.2f}",
            DocumentType.PAPER: f"检测到学术论文特征，置信度: {score:.2f}",
            DocumentType.REPORT: f"检测到报告文档特征，置信度: {score:.2f}",
            DocumentType.SUMMARY: f"检测到总结文档特征，置信度: {score:.2f}",
            DocumentType.ARTICLE: f"检测到文章特征，置信度: {score:.2f}",
            DocumentType.MANUAL: f"检测到手册/指南特征，置信度: {score:.2f}",
            DocumentType.PRESENTATION: f"检测到演示文稿特征，置信度: {score:.2f}",
            DocumentType.OTHER: f"未能明确分类，归为其他类型，置信度: {score:.2f}"
        }
        
        return explanations.get(document_type, f"文档类型: {document_type.value}, 置信度: {score:.2f}")
