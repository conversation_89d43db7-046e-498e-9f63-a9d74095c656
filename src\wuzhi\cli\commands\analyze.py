"""
文档分析命令
"""

import click
import asyncio
import time
from pathlib import Path

from ...core.logger import get_logger
from ...core.database import get_db_session
from ...core.models import Document

logger = get_logger(__name__)


@click.command('analyze')
@click.option('--all', 'analyze_all', is_flag=True, help='分析所有未分析的文档')
@click.option('--force', is_flag=True, help='强制重新分析已分析的文档')
@click.option('--limit', type=int, help='限制分析的文档数量')
@click.option('--file-id', type=int, multiple=True, help='指定要分析的文档ID（可多次使用）')
@click.option('--ai', is_flag=True, help='启用AI增强分析')
@click.option('--no-ai', is_flag=True, help='禁用AI增强分析')
@click.option('--verbose', '-v', is_flag=True, help='显示详细信息')
@click.pass_context
def analyze_command(ctx, analyze_all, force, limit, file_id, ai, no_ai, verbose):
    """分析文档内容"""
    try:
        # 处理AI选项
        use_ai = None
        if ai:
            use_ai = True
        elif no_ai:
            use_ai = False
        
        click.echo("开始文档分析...")
        
        # 获取要分析的文档
        documents = _get_documents_for_analysis(analyze_all, force, limit, file_id)
        
        if not documents:
            click.echo("没有找到需要分析的文档")
            return
        
        click.echo(f"找到 {len(documents)} 个文档需要分析")
        
        if use_ai is not None:
            ai_status = "启用" if use_ai else "禁用"
            click.echo(f"AI增强分析: {ai_status}")
        
        # 执行分析
        start_time = time.time()
        
        async def analyze_documents():
            from ...services.document_service import DocumentService
            
            service = DocumentService()
            
            success_count = 0
            error_count = 0
            
            for i, doc in enumerate(documents, 1):
                try:
                    if verbose:
                        click.echo(f"分析 {i}/{len(documents)}: {doc.file_name}")
                    
                    # 执行分析
                    result = await service.analyze_document_async(
                        doc.id, 
                        use_ai=use_ai,
                        force=force
                    )
                    
                    if result['success']:
                        success_count += 1
                        if verbose:
                            click.echo(f"  ✓ 分析成功")
                    else:
                        error_count += 1
                        if verbose:
                            click.echo(f"  ✗ 分析失败: {result.get('error', '未知错误')}")
                
                except Exception as e:
                    error_count += 1
                    if verbose:
                        click.echo(f"  ✗ 分析异常: {e}")
                    logger.error(f"分析文档失败 {doc.id}: {e}")
                
                # 显示进度
                if not verbose and i % 10 == 0:
                    click.echo(f"进度: {i}/{len(documents)}")
            
            return success_count, error_count
        
        # 运行异步分析
        success_count, error_count = asyncio.run(analyze_documents())
        
        elapsed_time = time.time() - start_time
        
        # 显示结果
        click.echo("\n" + "=" * 50)
        click.echo("分析完成！")
        click.echo("=" * 50)
        
        click.echo(f"总文档数: {len(documents)}")
        click.echo(f"成功分析: {success_count}")
        click.echo(f"分析失败: {error_count}")
        click.echo(f"耗时: {elapsed_time:.2f} 秒")
        
        if success_count > 0:
            avg_time = elapsed_time / success_count
            click.echo(f"平均每个文档: {avg_time:.2f} 秒")
        
        if error_count > 0:
            click.echo(f"\n警告: {error_count} 个文档分析失败")
            click.echo("请检查日志文件获取详细错误信息")
        
        # 建议下一步操作
        if success_count > 0:
            click.echo("\n下一步操作:")
            click.echo("1. 检测重复: wuzhi duplicate detect")
            click.echo("2. 查看状态: wuzhi status")
            click.echo("3. 启动图形界面: wuzhi gui")
        
    except KeyboardInterrupt:
        click.echo("\n分析被用户中断")
    except Exception as e:
        click.echo(f"分析失败: {e}", err=True)
        logger.error(f"分析失败: {e}")


def _get_documents_for_analysis(analyze_all, force, limit, file_ids):
    """获取要分析的文档列表"""
    try:
        with get_db_session() as session:
            query = session.query(Document)
            
            # 根据选项过滤
            if file_ids:
                # 指定文档ID
                query = query.filter(Document.id.in_(file_ids))
            elif analyze_all:
                # 所有文档
                if not force:
                    # 只分析未分析的文档
                    query = query.filter(Document.is_analyzed == False)
            else:
                # 默认：未分析的文档
                query = query.filter(Document.is_analyzed == False)
            
            # 应用限制
            if limit:
                query = query.limit(limit)
            
            documents = query.all()
            return documents
            
    except Exception as e:
        logger.error(f"获取文档列表失败: {e}")
        return []


@click.command('reanalyze')
@click.option('--all', 'reanalyze_all', is_flag=True, help='重新分析所有文档')
@click.option('--failed', is_flag=True, help='只重新分析失败的文档')
@click.option('--ai', is_flag=True, help='启用AI增强分析')
@click.pass_context
def reanalyze_command(ctx, reanalyze_all, failed, ai):
    """重新分析文档"""
    try:
        if not (reanalyze_all or failed):
            click.echo("请指定重新分析选项:")
            click.echo("  --all: 重新分析所有文档")
            click.echo("  --failed: 只重新分析失败的文档")
            return
        
        if reanalyze_all:
            click.echo("重新分析所有文档...")
            ctx.invoke(analyze_command, analyze_all=True, force=True, ai=ai)
        
        elif failed:
            click.echo("重新分析失败的文档...")
            
            # 获取分析失败的文档
            with get_db_session() as session:
                failed_docs = session.query(Document).filter(
                    Document.is_analyzed == False
                ).all()
                
                if not failed_docs:
                    click.echo("没有找到分析失败的文档")
                    return
                
                file_ids = [doc.id for doc in failed_docs]
                
                ctx.invoke(analyze_command, file_id=file_ids, force=True, ai=ai)
        
    except Exception as e:
        click.echo(f"重新分析失败: {e}", err=True)
        logger.error(f"重新分析失败: {e}")


@click.command('stats')
@click.pass_context
def stats_command(ctx):
    """显示分析统计信息"""
    try:
        from ...services.document_service import DocumentService
        
        service = DocumentService()
        stats = service.get_analysis_statistics()
        
        click.echo("文档分析统计:")
        click.echo("-" * 30)
        
        click.echo(f"总文档数: {stats.get('total_documents', 0)}")
        click.echo(f"已分析: {stats.get('analyzed_documents', 0)}")
        click.echo(f"未分析: {stats.get('unanalyzed_documents', 0)}")
        click.echo(f"分析成功率: {stats.get('analysis_success_rate', 0):.1%}")
        
        # 文档类型分布
        type_dist = stats.get('document_type_distribution', {})
        if type_dist:
            click.echo("\n文档类型分布:")
            for doc_type, count in type_dist.items():
                click.echo(f"  {doc_type}: {count}")
        
        # 语言分布
        lang_dist = stats.get('language_distribution', {})
        if lang_dist:
            click.echo("\n语言分布:")
            for lang, count in lang_dist.items():
                click.echo(f"  {lang}: {count}")
        
        # 质量统计
        avg_quality = stats.get('average_text_quality', 0)
        if avg_quality > 0:
            click.echo(f"\n平均文本质量: {avg_quality:.2f}")
        
        high_quality = stats.get('high_quality_documents', 0)
        if high_quality > 0:
            click.echo(f"高质量文档: {high_quality}")
        
    except Exception as e:
        click.echo(f"获取统计信息失败: {e}", err=True)
        logger.error(f"获取统计信息失败: {e}")


# 将子命令添加到analyze组
analyze_command.add_command(reanalyze_command)
analyze_command.add_command(stats_command)
