"""
数据库管理模块
"""

from contextlib import contextmanager
from typing import Generator
from sqlalchemy import create_engine, event
from sqlalchemy.engine import Engine
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool

from .config import config
from .logger import get_logger
from .models import Base

logger = get_logger(__name__)

# 数据库引擎
engine = None
SessionLocal = None


def init_database() -> None:
    """初始化数据库"""
    global engine, SessionLocal
    
    try:
        # 创建数据库引擎
        if config.database_url.startswith("sqlite"):
            engine = create_engine(
                config.database_url,
                poolclass=StaticPool,
                connect_args={
                    "check_same_thread": False,
                    "timeout": 20,
                },
                echo=config.debug,
            )
            
            # 启用SQLite外键约束
            @event.listens_for(engine, "connect")
            def set_sqlite_pragma(dbapi_connection, connection_record):
                cursor = dbapi_connection.cursor()
                cursor.execute("PRAGMA foreign_keys=ON")
                cursor.execute("PRAGMA journal_mode=WAL")
                cursor.execute("PRAGMA synchronous=NORMAL")
                cursor.execute("PRAGMA cache_size=10000")
                cursor.execute("PRAGMA temp_store=MEMORY")
                cursor.close()
        else:
            engine = create_engine(
                config.database_url,
                echo=config.debug,
            )
        
        # 创建会话工厂
        SessionLocal = sessionmaker(
            autocommit=False,
            autoflush=False,
            bind=engine
        )
        
        # 创建所有表
        Base.metadata.create_all(bind=engine)
        
        logger.info(f"数据库初始化成功: {config.database_url}")
        
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        raise


def get_engine() -> Engine:
    """获取数据库引擎"""
    if engine is None:
        init_database()
    return engine


@contextmanager
def get_db_session() -> Generator[Session, None, None]:
    """获取数据库会话上下文管理器"""
    if SessionLocal is None:
        init_database()
    
    session = SessionLocal()
    try:
        yield session
        session.commit()
    except Exception as e:
        session.rollback()
        logger.error(f"数据库操作失败: {e}")
        raise
    finally:
        session.close()


def get_db() -> Generator[Session, None, None]:
    """获取数据库会话（用于依赖注入）"""
    with get_db_session() as session:
        yield session


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        self.engine = get_engine()
        self.SessionLocal = SessionLocal
    
    @contextmanager
    def session(self) -> Generator[Session, None, None]:
        """获取数据库会话"""
        session = self.SessionLocal()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            logger.error(f"数据库操作失败: {e}")
            raise
        finally:
            session.close()
    
    def create_tables(self) -> None:
        """创建所有表"""
        Base.metadata.create_all(bind=self.engine)
        logger.info("数据库表创建完成")
    
    def drop_tables(self) -> None:
        """删除所有表"""
        Base.metadata.drop_all(bind=self.engine)
        logger.warning("数据库表已删除")
    
    def reset_database(self) -> None:
        """重置数据库"""
        self.drop_tables()
        self.create_tables()
        logger.info("数据库已重置")
    
    def backup_database(self, backup_path: str) -> None:
        """备份数据库"""
        # TODO: 实现数据库备份功能
        logger.info(f"数据库备份到: {backup_path}")
    
    def restore_database(self, backup_path: str) -> None:
        """恢复数据库"""
        # TODO: 实现数据库恢复功能
        logger.info(f"从备份恢复数据库: {backup_path}")
    
    def get_database_info(self) -> dict:
        """获取数据库信息"""
        with self.session() as session:
            # 获取表信息
            tables = Base.metadata.tables.keys()
            
            # 统计各表记录数
            table_counts = {}
            for table_name in tables:
                try:
                    table = Base.metadata.tables[table_name]
                    count = session.execute(f"SELECT COUNT(*) FROM {table_name}").scalar()
                    table_counts[table_name] = count
                except Exception as e:
                    logger.warning(f"获取表 {table_name} 记录数失败: {e}")
                    table_counts[table_name] = 0
            
            return {
                "database_url": str(self.engine.url),
                "tables": list(tables),
                "table_counts": table_counts,
                "total_records": sum(table_counts.values()),
            }


# 全局数据库管理器实例
db_manager = DatabaseManager()
