"""
元数据分析器
"""

import re
from pathlib import Path
from typing import Dict, List, Tuple, Optional
from datetime import datetime

from .base import BaseAnalyzer, AnalysisResult
from ..core.models import Language
from ..core.logger import get_logger
from ..extractors.base import ExtractionResult
from ..utils.text_utils import detect_language

logger = get_logger(__name__)


class MetadataAnalyzer(BaseAnalyzer):
    """元数据分析器"""
    
    def __init__(self):
        super().__init__()
        self._init_patterns()
    
    def _init_patterns(self):
        """初始化元数据提取模式"""
        # 标题提取模式
        self.title_patterns = [
            r'^(.{5,100})$',  # 单行标题
            r'标题[：:]\s*(.+)',
            r'Title[：:]\s*(.+)',
            r'题目[：:]\s*(.+)',
            r'Subject[：:]\s*(.+)',
        ]
        
        # 作者提取模式
        self.author_patterns = [
            r'作者[：:]\s*([^\n\r]{2,50})',
            r'著[：:]\s*([^\n\r]{2,50})',
            r'编著[：:]\s*([^\n\r]{2,50})',
            r'Author[：:]\s*([^\n\r]{2,50})',
            r'By[：:]\s*([^\n\r]{2,50})',
            r'Written\s+by[：:]\s*([^\n\r]{2,50})',
            r'撰写[：:]\s*([^\n\r]{2,50})',
        ]
        
        # 出版社提取模式
        self.publisher_patterns = [
            r'出版社[：:]\s*([^\n\r]{2,50})',
            r'出版[：:]\s*([^\n\r]{2,50})',
            r'Publisher[：:]\s*([^\n\r]{2,50})',
            r'Published\s+by[：:]\s*([^\n\r]{2,50})',
            r'([^\n\r]*出版社)',
            r'([^\n\r]*Press)',
            r'([^\n\r]*Publishing)',
        ]
        
        # 日期提取模式
        self.date_patterns = [
            r'(\d{4})[年\-/](\d{1,2})[月\-/](\d{1,2})[日]?',
            r'(\d{4})[年\-/](\d{1,2})[月]?',
            r'(\d{4})',
            r'日期[：:]\s*(\d{4}[年\-/]\d{1,2}[月\-/]?\d{0,2}[日]?)',
            r'Date[：:]\s*(\d{4}[年\-/]\d{1,2}[月\-/]?\d{0,2}[日]?)',
            r'发表于[：:]\s*(\d{4}[年\-/]\d{1,2}[月\-/]?\d{0,2}[日]?)',
            r'Published[：:]\s*(\d{4}[年\-/]\d{1,2}[月\-/]?\d{0,2}[日]?)',
        ]
        
        # 关键词提取模式
        self.keyword_patterns = [
            r'关键词[：:]\s*([^\n\r]+)',
            r'Keywords[：:]\s*([^\n\r]+)',
            r'主题词[：:]\s*([^\n\r]+)',
            r'标签[：:]\s*([^\n\r]+)',
            r'Tags[：:]\s*([^\n\r]+)',
        ]
    
    def analyze(self, extraction_result: ExtractionResult, file_path: Path = None) -> AnalysisResult:
        """分析文档元数据"""
        if not self.can_analyze(extraction_result):
            return self._create_error_result("无法分析元数据：内容为空或提取失败")
        
        try:
            text = extraction_result.plain_text
            
            # 提取各种元数据
            title, title_confidence = self._extract_title(text, extraction_result)
            author, author_confidence = self._extract_author(text, extraction_result)
            publisher = self._extract_publisher(text)
            publish_date = self._extract_publish_date(text)
            language = self._detect_document_language(text)
            
            # 计算整体置信度
            confidence = self._calculate_overall_confidence(
                title_confidence, author_confidence, extraction_result
            )
            
            return self._create_success_result(
                title=title,
                title_confidence=title_confidence,
                author=author,
                author_confidence=author_confidence,
                publisher=publisher,
                publish_date=publish_date,
                language=language,
                confidence=confidence,
                metadata={
                    'extraction_method': 'pattern_matching',
                    'has_embedded_metadata': bool(extraction_result.metadata),
                    'text_length': len(text),
                }
            )
            
        except Exception as e:
            logger.error(f"元数据分析失败: {e}")
            return self._create_error_result(f"分析失败: {e}")
    
    def _extract_title(self, text: str, extraction_result: ExtractionResult) -> Tuple[Optional[str], float]:
        """提取文档标题"""
        # 优先使用已提取的元数据
        if extraction_result.title:
            return extraction_result.title, 0.9
        
        # 优先使用第一个标题
        if extraction_result.headings:
            first_heading = extraction_result.headings[0]
            if first_heading:
                title = first_heading.strip().lstrip('#').strip()
                if 5 <= len(title) <= 200:
                    return title, 0.8
        
        # 使用模式匹配
        lines = text.split('\n')[:15]  # 只检查前15行
        
        for pattern in self.title_patterns:
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                
                match = re.search(pattern, line, re.IGNORECASE)
                if match:
                    title = match.group(1).strip()
                    if self._is_valid_title(title):
                        confidence = self._calculate_title_confidence(title, lines.index(line))
                        return title, confidence
        
        # 尝试从文档开头提取
        for i, line in enumerate(lines[:5]):
            line = line.strip()
            if self._is_valid_title(line):
                confidence = 0.6 - (i * 0.1)  # 越靠前置信度越高
                return line, max(confidence, 0.3)
        
        return None, 0.0
    
    def _extract_author(self, text: str, extraction_result: ExtractionResult) -> Tuple[Optional[str], float]:
        """提取作者信息"""
        # 优先使用已提取的元数据
        if extraction_result.author:
            return extraction_result.author, 0.9
        
        # 使用模式匹配
        lines = text.split('\n')[:25]  # 检查前25行
        text_to_search = '\n'.join(lines)
        
        for pattern in self.author_patterns:
            matches = re.findall(pattern, text_to_search, re.IGNORECASE)
            if matches:
                author = matches[0].strip()
                if self._is_valid_author(author):
                    confidence = self._calculate_author_confidence(author, pattern)
                    return author, confidence
        
        return None, 0.0
    
    def _extract_publisher(self, text: str) -> Optional[str]:
        """提取出版社信息"""
        lines = text.split('\n')[:30]  # 检查前30行
        text_to_search = '\n'.join(lines)
        
        for pattern in self.publisher_patterns:
            matches = re.findall(pattern, text_to_search, re.IGNORECASE)
            if matches:
                publisher = matches[0].strip()
                if self._is_valid_publisher(publisher):
                    return publisher
        
        return None
    
    def _extract_publish_date(self, text: str) -> Optional[str]:
        """提取出版日期"""
        lines = text.split('\n')[:30]  # 检查前30行
        text_to_search = '\n'.join(lines)
        
        for pattern in self.date_patterns:
            matches = re.findall(pattern, text_to_search)
            if matches:
                # 处理不同的日期格式
                if isinstance(matches[0], tuple):
                    # 年月日格式
                    if len(matches[0]) == 3:
                        year, month, day = matches[0]
                        return f"{year}-{month.zfill(2)}-{day.zfill(2)}"
                    elif len(matches[0]) == 2:
                        year, month = matches[0]
                        return f"{year}-{month.zfill(2)}"
                else:
                    # 单个匹配
                    date_str = matches[0]
                    if self._is_valid_date(date_str):
                        return self._normalize_date(date_str)
        
        return None
    
    def _detect_document_language(self, text: str) -> Optional[Language]:
        """检测文档语言"""
        try:
            return detect_language(text)
        except Exception as e:
            logger.debug(f"语言检测失败: {e}")
            return None
    
    def _is_valid_title(self, title: str) -> bool:
        """验证标题是否有效"""
        if not title or len(title) < 5 or len(title) > 200:
            return False
        
        # 排除明显不是标题的内容
        invalid_patterns = [
            r'^https?://',  # URL
            r'^\d+$',       # 纯数字
            r'^[^\w\u4e00-\u9fff]+$',  # 只有标点符号
            r'^\s*$',       # 空白
        ]
        
        for pattern in invalid_patterns:
            if re.match(pattern, title):
                return False
        
        return True
    
    def _is_valid_author(self, author: str) -> bool:
        """验证作者是否有效"""
        if not author or len(author) < 2 or len(author) > 50:
            return False
        
        # 排除明显不是作者的内容
        invalid_patterns = [
            r'^\d+$',       # 纯数字
            r'^[^\w\u4e00-\u9fff]+$',  # 只有标点符号
            r'^\s*$',       # 空白
            r'http',        # 包含URL
        ]
        
        for pattern in invalid_patterns:
            if re.search(pattern, author):
                return False
        
        return True
    
    def _is_valid_publisher(self, publisher: str) -> bool:
        """验证出版社是否有效"""
        if not publisher or len(publisher) < 3 or len(publisher) > 50:
            return False
        
        # 出版社通常包含特定词汇
        publisher_indicators = ['出版', 'press', 'publishing', '社', 'house']
        return any(indicator in publisher.lower() for indicator in publisher_indicators)
    
    def _is_valid_date(self, date_str: str) -> bool:
        """验证日期是否有效"""
        if not date_str:
            return False
        
        # 提取年份
        year_match = re.search(r'(\d{4})', date_str)
        if year_match:
            year = int(year_match.group(1))
            # 合理的年份范围
            return 1900 <= year <= datetime.now().year + 1
        
        return False
    
    def _normalize_date(self, date_str: str) -> str:
        """标准化日期格式"""
        # 将各种日期格式转换为标准格式
        date_str = date_str.replace('年', '-').replace('月', '-').replace('日', '')
        date_str = date_str.replace('/', '-')
        
        # 移除多余的分隔符
        date_str = re.sub(r'-+', '-', date_str)
        date_str = date_str.strip('-')
        
        return date_str
    
    def _calculate_title_confidence(self, title: str, position: int) -> float:
        """计算标题置信度"""
        confidence = 0.5
        
        # 位置因素
        if position == 0:
            confidence += 0.3
        elif position <= 2:
            confidence += 0.2
        elif position <= 5:
            confidence += 0.1
        
        # 长度因素
        if 10 <= len(title) <= 100:
            confidence += 0.1
        
        # 格式因素
        if title[0].isupper() or '\u4e00' <= title[0] <= '\u9fff':
            confidence += 0.1
        
        return min(confidence, 1.0)
    
    def _calculate_author_confidence(self, author: str, pattern: str) -> float:
        """计算作者置信度"""
        confidence = 0.5
        
        # 模式匹配质量
        if '作者' in pattern or 'Author' in pattern:
            confidence += 0.3
        elif '著' in pattern or 'By' in pattern:
            confidence += 0.2
        
        # 长度因素
        if 2 <= len(author) <= 20:
            confidence += 0.1
        
        # 格式因素（中文姓名或英文姓名）
        if (re.match(r'^[\u4e00-\u9fff]{2,4}$', author) or  # 中文姓名
            re.match(r'^[A-Za-z\s\.]{3,30}$', author)):     # 英文姓名
            confidence += 0.1
        
        return min(confidence, 1.0)
    
    def _calculate_overall_confidence(self, title_confidence: float, author_confidence: float, 
                                    extraction_result: ExtractionResult) -> float:
        """计算整体置信度"""
        confidences = []
        
        if title_confidence > 0:
            confidences.append(title_confidence)
        if author_confidence > 0:
            confidences.append(author_confidence)
        
        # 如果有嵌入的元数据，增加置信度
        if extraction_result.metadata:
            confidences.append(0.7)
        
        # 如果有结构化信息，增加置信度
        if extraction_result.headings:
            confidences.append(0.6)
        
        if not confidences:
            return 0.0
        
        return sum(confidences) / len(confidences)
