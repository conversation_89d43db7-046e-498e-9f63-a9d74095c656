# 悟知 (<PERSON><PERSON><PERSON>) Docker 镜像
FROM python:3.11-slim

# 设置环境变量
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    curl \
    build-essential \
    libmagic1 \
    libmagic-dev \
    && rm -rf /var/lib/apt/lists/*

# 安装Poetry
RUN pip install poetry

# 配置Poetry
RUN poetry config virtualenvs.create false

# 复制项目文件
COPY pyproject.toml poetry.lock* ./

# 安装Python依赖
RUN poetry install --no-dev --no-interaction --no-ansi

# 复制应用代码
COPY src/ ./src/
COPY .env.example .env

# 创建必要的目录
RUN mkdir -p data logs config cache temp

# 设置权限
RUN chmod -R 755 /app

# 暴露端口
EXPOSE 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# 启动命令
CMD ["python", "-m", "wuzhi.cli.main", "serve", "--host", "0.0.0.0", "--port", "8080"]
