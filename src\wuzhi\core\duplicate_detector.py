"""
重复文档检测器
"""

import hashlib
from pathlib import Path
from typing import Dict, List, Tuple, Set, Optional
from dataclasses import dataclass
from collections import defaultdict

from .config import config
from .logger import get_logger
from .models import Document
from .database import get_db_session
from ..utils.text_utils import calculate_text_similarity

logger = get_logger(__name__)


@dataclass
class DuplicateMatch:
    """重复匹配结果"""
    doc1_id: int
    doc2_id: int
    doc1_path: str
    doc2_path: str
    similarity_score: float
    match_type: str  # 'exact', 'hash', 'content', 'fuzzy'
    confidence: float
    
    def __post_init__(self):
        # 确保doc1_id <= doc2_id，避免重复
        if self.doc1_id > self.doc2_id:
            self.doc1_id, self.doc2_id = self.doc2_id, self.doc1_id
            self.doc1_path, self.doc2_path = self.doc2_path, self.doc1_path


@dataclass
class DuplicateGroup:
    """重复文档组"""
    group_id: str
    documents: List[int]  # 文档ID列表
    similarity_score: float
    match_type: str
    representative_doc: int  # 代表文档ID
    
    def size(self) -> int:
        return len(self.documents)
    
    def contains(self, doc_id: int) -> bool:
        return doc_id in self.documents


class DuplicateDetector:
    """重复文档检测器"""
    
    def __init__(self):
        self.similarity_threshold = config.similarity_threshold
        self.hash_cache = {}  # 文件哈希缓存
        self.content_cache = {}  # 内容缓存
    
    def detect_duplicates(self, document_ids: List[int] = None) -> List[DuplicateGroup]:
        """检测重复文档"""
        try:
            logger.info("开始重复文档检测")
            
            # 获取要检测的文档
            documents = self._get_documents_for_detection(document_ids)
            if len(documents) < 2:
                logger.info("文档数量不足，无需检测重复")
                return []
            
            logger.info(f"检测 {len(documents)} 个文档的重复性")
            
            # 多层次检测
            matches = []
            
            # 1. 精确匹配（文件哈希）
            hash_matches = self._detect_by_hash(documents)
            matches.extend(hash_matches)
            logger.info(f"哈希匹配找到 {len(hash_matches)} 对重复文档")
            
            # 2. 内容相似度匹配
            content_matches = self._detect_by_content_similarity(documents)
            matches.extend(content_matches)
            logger.info(f"内容相似度匹配找到 {len(content_matches)} 对重复文档")
            
            # 3. 文件名相似度匹配
            name_matches = self._detect_by_filename_similarity(documents)
            matches.extend(name_matches)
            logger.info(f"文件名相似度匹配找到 {len(name_matches)} 对重复文档")
            
            # 合并匹配结果为组
            groups = self._group_matches(matches)
            logger.info(f"总共发现 {len(groups)} 个重复文档组")
            
            return groups
            
        except Exception as e:
            logger.error(f"重复文档检测失败: {e}")
            return []
    
    def _get_documents_for_detection(self, document_ids: List[int] = None) -> List[Document]:
        """获取要检测的文档"""
        try:
            with get_db_session() as session:
                query = session.query(Document)
                
                if document_ids:
                    query = query.filter(Document.id.in_(document_ids))
                
                # 只检测已分析的文档
                query = query.filter(Document.is_analyzed == True)
                
                documents = query.all()
                return documents
                
        except Exception as e:
            logger.error(f"获取文档列表失败: {e}")
            return []
    
    def _detect_by_hash(self, documents: List[Document]) -> List[DuplicateMatch]:
        """基于文件哈希检测重复"""
        matches = []
        hash_groups = defaultdict(list)
        
        # 按哈希分组
        for doc in documents:
            if doc.file_hash:
                hash_groups[doc.file_hash].append(doc)
        
        # 找出重复组
        for file_hash, docs in hash_groups.items():
            if len(docs) > 1:
                # 生成所有配对
                for i in range(len(docs)):
                    for j in range(i + 1, len(docs)):
                        match = DuplicateMatch(
                            doc1_id=docs[i].id,
                            doc2_id=docs[j].id,
                            doc1_path=docs[i].file_path,
                            doc2_path=docs[j].file_path,
                            similarity_score=1.0,  # 哈希相同，完全相同
                            match_type='hash',
                            confidence=1.0
                        )
                        matches.append(match)
        
        return matches
    
    def _detect_by_content_similarity(self, documents: List[Document]) -> List[DuplicateMatch]:
        """基于内容相似度检测重复"""
        matches = []
        
        # 获取文档内容
        doc_contents = {}
        for doc in documents:
            content = self._get_document_content(doc)
            if content and len(content.strip()) > 100:  # 内容足够长才比较
                doc_contents[doc.id] = content
        
        if len(doc_contents) < 2:
            return matches
        
        # 计算两两相似度
        doc_ids = list(doc_contents.keys())
        for i in range(len(doc_ids)):
            for j in range(i + 1, len(doc_ids)):
                doc1_id, doc2_id = doc_ids[i], doc_ids[j]
                
                try:
                    similarity = calculate_text_similarity(
                        doc_contents[doc1_id],
                        doc_contents[doc2_id]
                    )
                    
                    if similarity >= self.similarity_threshold:
                        # 找到对应的文档对象
                        doc1 = next(d for d in documents if d.id == doc1_id)
                        doc2 = next(d for d in documents if d.id == doc2_id)
                        
                        match = DuplicateMatch(
                            doc1_id=doc1_id,
                            doc2_id=doc2_id,
                            doc1_path=doc1.file_path,
                            doc2_path=doc2.file_path,
                            similarity_score=similarity,
                            match_type='content',
                            confidence=similarity
                        )
                        matches.append(match)
                        
                except Exception as e:
                    logger.debug(f"计算相似度失败 {doc1_id}-{doc2_id}: {e}")
                    continue
        
        return matches
    
    def _detect_by_filename_similarity(self, documents: List[Document]) -> List[DuplicateMatch]:
        """基于文件名相似度检测重复"""
        matches = []
        
        # 简单的文件名匹配
        name_groups = defaultdict(list)
        
        for doc in documents:
            # 提取文件名（不含扩展名）
            file_path = Path(doc.file_path)
            name_without_ext = file_path.stem.lower()
            
            # 移除常见的版本号、日期等后缀
            cleaned_name = self._clean_filename(name_without_ext)
            
            if len(cleaned_name) > 3:  # 文件名足够长
                name_groups[cleaned_name].append(doc)
        
        # 找出同名文件
        for cleaned_name, docs in name_groups.items():
            if len(docs) > 1:
                # 检查文件大小是否相似
                for i in range(len(docs)):
                    for j in range(i + 1, len(docs)):
                        doc1, doc2 = docs[i], docs[j]
                        
                        # 文件大小相似度
                        size_ratio = min(doc1.file_size, doc2.file_size) / max(doc1.file_size, doc2.file_size)
                        
                        if size_ratio > 0.8:  # 文件大小相似
                            match = DuplicateMatch(
                                doc1_id=doc1.id,
                                doc2_id=doc2.id,
                                doc1_path=doc1.file_path,
                                doc2_path=doc2.file_path,
                                similarity_score=size_ratio,
                                match_type='filename',
                                confidence=size_ratio * 0.7  # 文件名匹配置信度较低
                            )
                            matches.append(match)
        
        return matches
    
    def _clean_filename(self, filename: str) -> str:
        """清理文件名"""
        import re
        
        # 移除版本号
        filename = re.sub(r'[_\-\s]*v?\d+(\.\d+)*[_\-\s]*', '', filename)
        
        # 移除日期
        filename = re.sub(r'[_\-\s]*\d{4}[_\-]\d{1,2}[_\-]\d{1,2}[_\-\s]*', '', filename)
        filename = re.sub(r'[_\-\s]*\d{8}[_\-\s]*', '', filename)
        
        # 移除常见后缀
        suffixes = ['copy', 'backup', 'bak', '副本', '备份', 'new', 'old', 'final', 'draft']
        for suffix in suffixes:
            filename = re.sub(rf'[_\-\s]*{suffix}[_\-\s]*', '', filename, flags=re.IGNORECASE)
        
        # 移除多余的分隔符
        filename = re.sub(r'[_\-\s]+', '_', filename)
        filename = filename.strip('_-')
        
        return filename
    
    def _get_document_content(self, doc: Document) -> Optional[str]:
        """获取文档内容"""
        # 这里应该从数据库或缓存中获取文档的纯文本内容
        # 简化实现，返回None
        # 在实际应用中，可以从分析结果中获取
        return None
    
    def _group_matches(self, matches: List[DuplicateMatch]) -> List[DuplicateGroup]:
        """将匹配结果分组"""
        if not matches:
            return []
        
        # 使用并查集算法分组
        parent = {}
        
        def find(x):
            if x not in parent:
                parent[x] = x
            if parent[x] != x:
                parent[x] = find(parent[x])
            return parent[x]
        
        def union(x, y):
            px, py = find(x), find(y)
            if px != py:
                parent[px] = py
        
        # 合并相关的文档
        for match in matches:
            union(match.doc1_id, match.doc2_id)
        
        # 按组收集文档
        groups_dict = defaultdict(list)
        for match in matches:
            root = find(match.doc1_id)
            groups_dict[root].append(match.doc1_id)
            groups_dict[root].append(match.doc2_id)
        
        # 去重并创建组对象
        groups = []
        for root, doc_ids in groups_dict.items():
            unique_docs = list(set(doc_ids))
            if len(unique_docs) > 1:
                # 计算组的平均相似度
                group_matches = [m for m in matches if m.doc1_id in unique_docs and m.doc2_id in unique_docs]
                avg_similarity = sum(m.similarity_score for m in group_matches) / len(group_matches)
                
                # 选择代表文档（通常是第一个）
                representative = min(unique_docs)
                
                # 确定匹配类型（优先级：hash > content > filename）
                match_types = [m.match_type for m in group_matches]
                if 'hash' in match_types:
                    group_type = 'hash'
                elif 'content' in match_types:
                    group_type = 'content'
                else:
                    group_type = 'filename'
                
                group = DuplicateGroup(
                    group_id=f"dup_{root}",
                    documents=sorted(unique_docs),
                    similarity_score=avg_similarity,
                    match_type=group_type,
                    representative_doc=representative
                )
                groups.append(group)
        
        # 按相似度排序
        groups.sort(key=lambda g: g.similarity_score, reverse=True)
        
        return groups
    
    def mark_duplicates_in_database(self, groups: List[DuplicateGroup]) -> int:
        """在数据库中标记重复文档"""
        marked_count = 0
        
        try:
            with get_db_session() as session:
                for group in groups:
                    # 标记组中的所有文档为重复（除了代表文档）
                    for doc_id in group.documents:
                        if doc_id != group.representative_doc:
                            doc = session.query(Document).filter(Document.id == doc_id).first()
                            if doc:
                                doc.is_duplicate = True
                                marked_count += 1
                
                session.commit()
                logger.info(f"标记了 {marked_count} 个重复文档")
                
        except Exception as e:
            logger.error(f"标记重复文档失败: {e}")
        
        return marked_count
    
    def get_duplicate_statistics(self) -> Dict[str, int]:
        """获取重复文档统计"""
        try:
            with get_db_session() as session:
                total_docs = session.query(Document).count()
                duplicate_docs = session.query(Document).filter(Document.is_duplicate == True).count()
                
                return {
                    'total_documents': total_docs,
                    'duplicate_documents': duplicate_docs,
                    'unique_documents': total_docs - duplicate_docs,
                    'duplicate_ratio': duplicate_docs / total_docs if total_docs > 0 else 0,
                }
                
        except Exception as e:
            logger.error(f"获取重复文档统计失败: {e}")
            return {}
    
    def clear_duplicate_marks(self) -> int:
        """清除所有重复标记"""
        try:
            with get_db_session() as session:
                count = session.query(Document).filter(Document.is_duplicate == True).count()
                session.query(Document).update({Document.is_duplicate: False})
                session.commit()
                
                logger.info(f"清除了 {count} 个重复标记")
                return count
                
        except Exception as e:
            logger.error(f"清除重复标记失败: {e}")
            return 0


# 全局重复检测器实例
duplicate_detector = DuplicateDetector()
