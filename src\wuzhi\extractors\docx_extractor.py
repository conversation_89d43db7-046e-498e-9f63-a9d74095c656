"""
DOCX文件内容提取器
"""

from pathlib import Path
from typing import List, Dict, Any
from datetime import datetime
from docx import Document
from docx.oxml.ns import qn

from .base import BaseExtractor, ExtractionResult
from ..core.models import FileType
from ..core.logger import get_logger

logger = get_logger(__name__)


class DOCXExtractor(BaseExtractor):
    """DOCX文件提取器"""
    
    def __init__(self):
        super().__init__()
        self.supported_types = [FileType.DOCX]
    
    def can_extract(self, file_path: Path, file_type: FileType) -> bool:
        """检查是否可以提取指定文件"""
        return file_type in self.supported_types and file_path.exists()
    
    def extract(self, file_path: Path) -> ExtractionResult:
        """提取DOCX文件内容"""
        try:
            # 打开DOCX文档
            doc = Document(str(file_path))
            
            # 提取元数据
            metadata = self._extract_docx_metadata(doc)
            
            # 提取文本内容
            text_content, paragraphs = self._extract_text_content(doc)
            
            # 提取结构化信息
            headings = self._extract_headings(doc)
            tables = self._extract_tables(doc)
            images = self._extract_images(doc)
            
            if not text_content.strip():
                return self._create_error_result("文档内容为空")
            
            # 清理文本
            cleaned_text = self._clean_text(text_content)
            
            return self._create_success_result(
                text_content=text_content,
                plain_text=cleaned_text,
                title=metadata.get('title'),
                author=metadata.get('author'),
                subject=metadata.get('subject'),
                creator=metadata.get('creator'),
                creation_date=metadata.get('creation_date'),
                modification_date=metadata.get('modification_date'),
                paragraphs=paragraphs,
                headings=headings,
                tables=tables,
                images=images,
                metadata={
                    'extractor': 'python-docx',
                    'paragraph_count': len(doc.paragraphs),
                    'table_count': len(doc.tables),
                    **metadata
                }
            )
            
        except Exception as e:
            logger.error(f"DOCX文件提取失败 {file_path}: {e}")
            return self._create_error_result(f"提取失败: {e}")
    
    def _extract_docx_metadata(self, doc: Document) -> Dict[str, Any]:
        """提取DOCX元数据"""
        metadata = {}
        
        try:
            core_props = doc.core_properties
            
            # 基本元数据
            if core_props.title:
                metadata['title'] = core_props.title
            if core_props.author:
                metadata['author'] = core_props.author
            if core_props.subject:
                metadata['subject'] = core_props.subject
            if core_props.creator:
                metadata['creator'] = core_props.creator
            if core_props.keywords:
                metadata['keywords'] = core_props.keywords
            if core_props.comments:
                metadata['comments'] = core_props.comments
            if core_props.category:
                metadata['category'] = core_props.category
            if core_props.language:
                metadata['language'] = core_props.language
            
            # 日期信息
            if core_props.created:
                metadata['creation_date'] = core_props.created
            if core_props.modified:
                metadata['modification_date'] = core_props.modified
            if core_props.last_printed:
                metadata['last_printed'] = core_props.last_printed
            
            # 统计信息
            if hasattr(core_props, 'revision'):
                metadata['revision'] = core_props.revision
            
        except Exception as e:
            logger.warning(f"提取DOCX元数据失败: {e}")
        
        return metadata
    
    def _extract_text_content(self, doc: Document) -> tuple:
        """提取文本内容和段落"""
        text_content = ""
        paragraphs = []
        
        for paragraph in doc.paragraphs:
            para_text = paragraph.text.strip()
            if para_text:
                text_content += para_text + "\n"
                paragraphs.append(para_text)
        
        # 提取表格中的文本
        for table in doc.tables:
            for row in table.rows:
                row_text = []
                for cell in row.cells:
                    cell_text = cell.text.strip()
                    if cell_text:
                        row_text.append(cell_text)
                if row_text:
                    table_text = " | ".join(row_text)
                    text_content += table_text + "\n"
        
        return text_content, paragraphs
    
    def _extract_headings(self, doc: Document) -> List[str]:
        """提取标题"""
        headings = []
        
        for paragraph in doc.paragraphs:
            if paragraph.style.name.startswith('Heading'):
                heading_text = paragraph.text.strip()
                if heading_text:
                    headings.append(heading_text)
        
        return headings
    
    def _extract_tables(self, doc: Document) -> List[Dict]:
        """提取表格信息"""
        tables = []
        
        for table_index, table in enumerate(doc.tables):
            table_data = {
                'index': table_index,
                'rows': len(table.rows),
                'columns': len(table.columns) if table.rows else 0,
                'data': []
            }
            
            # 提取表格数据
            for row_index, row in enumerate(table.rows):
                row_data = []
                for cell in row.cells:
                    cell_text = cell.text.strip()
                    row_data.append(cell_text)
                table_data['data'].append(row_data)
            
            tables.append(table_data)
        
        return tables
    
    def _extract_images(self, doc: Document) -> List[Dict]:
        """提取图片信息"""
        images = []
        
        try:
            # 遍历文档中的所有关系
            for rel in doc.part.rels.values():
                if "image" in rel.target_ref:
                    image_info = {
                        'target': rel.target_ref,
                        'type': rel.reltype,
                    }
                    
                    # 尝试获取图片的更多信息
                    try:
                        image_part = rel.target_part
                        if hasattr(image_part, 'blob'):
                            image_info['size'] = len(image_part.blob)
                        if hasattr(image_part, 'content_type'):
                            image_info['content_type'] = image_part.content_type
                    except:
                        pass
                    
                    images.append(image_info)
        
        except Exception as e:
            logger.debug(f"提取图片信息失败: {e}")
        
        return images
    
    def _get_paragraph_style(self, paragraph) -> str:
        """获取段落样式"""
        try:
            return paragraph.style.name
        except:
            return "Normal"
    
    def _extract_hyperlinks(self, doc: Document) -> List[Dict]:
        """提取超链接"""
        links = []
        
        try:
            # 这需要更复杂的XML解析
            # 简化实现，只返回空列表
            pass
        except Exception as e:
            logger.debug(f"提取超链接失败: {e}")
        
        return links
