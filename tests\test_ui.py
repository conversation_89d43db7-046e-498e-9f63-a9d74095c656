"""
图形用户界面测试
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path

# 模拟flet模块
flet_mock = MagicMock()
flet_mock.Page = Mock
flet_mock.Text = Mock
flet_mock.Column = Mock
flet_mock.Row = Mock
flet_mock.Card = Mock
flet_mock.Container = Mock
flet_mock.ElevatedButton = Mock
flet_mock.NavigationRail = Mock
flet_mock.NavigationRailDestination = Mock
flet_mock.AlertDialog = Mock
flet_mock.TextButton = Mock
flet_mock.Checkbox = Mock
flet_mock.TextField = Mock
flet_mock.Dropdown = Mock
flet_mock.ProgressBar = Mock
flet_mock.ListView = Mock
flet_mock.ListTile = Mock
flet_mock.Icon = Mock
flet_mock.Divider = Mock
flet_mock.VerticalDivider = Mock
flet_mock.Chip = Mock
flet_mock.TreeView = Mock
flet_mock.TreeNode = Mock
flet_mock.FilePicker = Mock
flet_mock.FilePickerResultEvent = Mock

# 模拟flet的常量和枚举
flet_mock.ThemeMode = Mock()
flet_mock.ThemeMode.SYSTEM = "system"
flet_mock.ThemeMode.LIGHT = "light"
flet_mock.ThemeMode.DARK = "dark"

flet_mock.FontWeight = Mock()
flet_mock.FontWeight.BOLD = "bold"

flet_mock.NavigationRailLabelType = Mock()
flet_mock.NavigationRailLabelType.ALL = "all"

flet_mock.CrossAxisAlignment = Mock()
flet_mock.CrossAxisAlignment.CENTER = "center"

flet_mock.MainAxisAlignment = Mock()
flet_mock.MainAxisAlignment.START = "start"
flet_mock.MainAxisAlignment.END = "end"

flet_mock.TextOverflow = Mock()
flet_mock.TextOverflow.ELLIPSIS = "ellipsis"

flet_mock.AppView = Mock()
flet_mock.AppView.FLET_APP = "flet_app"
flet_mock.AppView.FLET_APP_HIDDEN = "flet_app_hidden"

flet_mock.colors = Mock()
flet_mock.colors.BLUE = "blue"
flet_mock.colors.GREEN = "green"
flet_mock.colors.RED = "red"
flet_mock.colors.ORANGE = "orange"
flet_mock.colors.PURPLE = "purple"
flet_mock.colors.GREY_600 = "grey_600"
flet_mock.colors.GREY_700 = "grey_700"
flet_mock.colors.GREY_800 = "grey_800"
flet_mock.colors.GREEN_100 = "green_100"
flet_mock.colors.GREEN_800 = "green_800"
flet_mock.colors.ORANGE_100 = "orange_100"
flet_mock.colors.ORANGE_800 = "orange_800"
flet_mock.colors.RED_100 = "red_100"
flet_mock.colors.RED_800 = "red_800"
flet_mock.colors.SURFACE_VARIANT = "surface_variant"

flet_mock.icons = Mock()
flet_mock.icons.HOME = "home"
flet_mock.icons.HOME_OUTLINED = "home_outlined"
flet_mock.icons.FOLDER = "folder"
flet_mock.icons.FOLDER_OUTLINED = "folder_outlined"
flet_mock.icons.FOLDER_OPEN = "folder_open"
flet_mock.icons.ANALYTICS = "analytics"
flet_mock.icons.ANALYTICS_OUTLINED = "analytics_outlined"
flet_mock.icons.TAG = "tag"
flet_mock.icons.TAG_OUTLINED = "tag_outlined"
flet_mock.icons.COPY = "copy"
flet_mock.icons.COPY_OUTLINED = "copy_outlined"
flet_mock.icons.SETTINGS = "settings"
flet_mock.icons.SETTINGS_OUTLINED = "settings_outlined"
flet_mock.icons.DESCRIPTION = "description"
flet_mock.icons.CHECK_CIRCLE = "check_circle"
flet_mock.icons.SEARCH = "search"
flet_mock.icons.PLAY_ARROW = "play_arrow"
flet_mock.icons.CLEAR = "clear"
flet_mock.icons.DELETE = "delete"
flet_mock.icons.SAVE = "save"
flet_mock.icons.PICTURE_AS_PDF = "picture_as_pdf"
flet_mock.icons.TEXT_SNIPPET = "text_snippet"
flet_mock.icons.MENU_BOOK = "menu_book"
flet_mock.icons.INSERT_DRIVE_FILE = "insert_drive_file"
flet_mock.icons.STAR = "star"

flet_mock.Theme = Mock
flet_mock.app = Mock
flet_mock.dropdown = Mock()
flet_mock.dropdown.Option = Mock

# 应用模拟
import sys
sys.modules['flet'] = flet_mock

# 现在可以导入我们的模块
from src.wuzhi.ui.app import WuZhiApp
from src.wuzhi.ui.components.dialogs import ScanDialog, AnalyzeDialog, ProgressDialog, SettingsDialog
from src.wuzhi.ui.components.widgets import DocumentCard, StatCard, SearchBar


class TestWuZhiApp:
    """WuZhi应用测试"""
    
    def setup_method(self):
        self.app = WuZhiApp()
        self.mock_page = Mock()
        self.mock_page.dialog = None
        self.mock_page.overlay = []
    
    def test_app_initialization(self):
        """测试应用初始化"""
        assert self.app.page is None
        assert self.app.current_view == "home"
        assert hasattr(self.app, 'document_service')
        assert hasattr(self.app, 'duplicate_service')
    
    @patch('src.wuzhi.ui.app.init_database')
    def test_main_method(self, mock_init_db):
        """测试主方法"""
        mock_init_db.return_value = True
        
        self.app.main(self.mock_page)
        
        assert self.app.page == self.mock_page
        # 验证页面设置被调用
        assert self.mock_page.add.called
        assert self.mock_page.update.called
    
    def test_nav_change(self):
        """测试导航变化"""
        self.app.page = self.mock_page
        self.app.content_area = Mock()
        
        # 模拟导航事件
        mock_event = Mock()
        mock_event.control.selected_index = 1
        
        self.app.on_nav_change(mock_event)
        
        assert self.app.current_view == "documents"
        assert self.mock_page.update.called
    
    def test_scan_click(self):
        """测试扫描点击事件"""
        self.app.page = self.mock_page
        
        with patch('src.wuzhi.ui.app.ScanDialog') as mock_dialog_class:
            mock_dialog = Mock()
            mock_dialog_class.return_value = mock_dialog
            
            self.app.on_scan_click(Mock())
            
            mock_dialog_class.assert_called_once()
            mock_dialog.show.assert_called_once()
    
    def test_analyze_click(self):
        """测试分析点击事件"""
        self.app.page = self.mock_page
        
        with patch('src.wuzhi.ui.app.AnalyzeDialog') as mock_dialog_class:
            mock_dialog = Mock()
            mock_dialog_class.return_value = mock_dialog
            
            self.app.on_analyze_click(Mock())
            
            mock_dialog_class.assert_called_once()
            mock_dialog.show.assert_called_once()
    
    def test_show_error(self):
        """测试错误显示"""
        self.app.page = self.mock_page
        
        self.app._show_error("测试错误")
        
        assert self.mock_page.dialog is not None
        assert self.mock_page.update.called
    
    def test_show_info(self):
        """测试信息显示"""
        self.app.page = self.mock_page
        
        self.app._show_info("测试信息")
        
        assert self.mock_page.dialog is not None
        assert self.mock_page.update.called


class TestScanDialog:
    """扫描对话框测试"""
    
    def setup_method(self):
        self.mock_page = Mock()
        self.mock_page.overlay = []
        self.mock_page.dialog = None
        self.on_scan_callback = Mock()
        
        self.dialog = ScanDialog(self.mock_page, self.on_scan_callback)
    
    def test_dialog_initialization(self):
        """测试对话框初始化"""
        assert self.dialog.page == self.mock_page
        assert self.dialog.on_scan == self.on_scan_callback
        assert self.dialog.selected_paths == []
        assert self.dialog.dialog is not None
    
    def test_clear_paths(self):
        """测试清空路径"""
        self.dialog.selected_paths = [Path("/test1"), Path("/test2")]
        
        self.dialog.clear_paths(Mock())
        
        assert len(self.dialog.selected_paths) == 0
    
    def test_remove_path(self):
        """测试移除路径"""
        test_path = Path("/test")
        self.dialog.selected_paths = [test_path, Path("/other")]
        
        self.dialog.remove_path(test_path)
        
        assert test_path not in self.dialog.selected_paths
        assert len(self.dialog.selected_paths) == 1
    
    def test_start_scan_no_paths(self):
        """测试无路径时开始扫描"""
        with patch.object(self.dialog, 'show_error') as mock_show_error:
            self.dialog.start_scan(Mock())
            
            mock_show_error.assert_called_once_with("请至少选择一个目录")
            self.on_scan_callback.assert_not_called()
    
    def test_start_scan_with_paths(self):
        """测试有路径时开始扫描"""
        test_paths = [Path("/test1"), Path("/test2")]
        self.dialog.selected_paths = test_paths
        
        with patch.object(self.dialog, 'close_dialog') as mock_close:
            self.dialog.start_scan(Mock())
            
            self.on_scan_callback.assert_called_once_with(test_paths, True)
            mock_close.assert_called_once()


class TestDocumentCard:
    """文档卡片测试"""
    
    def test_card_creation(self):
        """测试卡片创建"""
        document_data = {
            'id': 1,
            'title': '测试文档',
            'file_name': 'test.pdf',
            'author': '测试作者',
            'document_type': 'book',
            'file_size': 1024,
            'is_analyzed': True,
            'is_duplicate': False,
        }
        
        card = DocumentCard(document_data)
        
        assert card.document_data == document_data
        assert card.card is not None
    
    def test_format_size(self):
        """测试文件大小格式化"""
        document_data = {'file_size': 1024}
        card = DocumentCard(document_data)
        
        assert card._format_size(0) == "0 B"
        assert card._format_size(1024) == "1.0 KB"
        assert card._format_size(1024 * 1024) == "1.0 MB"
        assert card._format_size(1024 * 1024 * 1024) == "1.0 GB"


class TestStatCard:
    """统计卡片测试"""
    
    def test_stat_card_creation(self):
        """测试统计卡片创建"""
        card = StatCard("测试标题", "100", "test_icon", "blue")
        
        assert card.title == "测试标题"
        assert card.value == "100"
        assert card.icon == "test_icon"
        assert card.color == "blue"
        assert card.card is not None
    
    def test_update_value(self):
        """测试更新数值"""
        card = StatCard("测试", "100", "icon")
        
        card.update_value("200")
        
        assert card.value == "200"


class TestSearchBar:
    """搜索栏测试"""
    
    def test_search_bar_creation(self):
        """测试搜索栏创建"""
        on_search = Mock()
        on_filter = Mock()
        
        search_bar = SearchBar(on_search, on_filter)
        
        assert search_bar.on_search == on_search
        assert search_bar.on_filter == on_filter
        assert search_bar.container is not None
    
    def test_perform_search(self):
        """测试执行搜索"""
        on_search = Mock()
        search_bar = SearchBar(on_search)
        
        search_bar.search_field.value = "测试查询"
        search_bar.type_filter.value = "book"
        search_bar.status_filter.value = "analyzed"
        
        search_bar._perform_search()
        
        on_search.assert_called_once()
        call_args = on_search.call_args
        assert call_args[0][0] == "测试查询"  # query
        assert call_args[0][1]['document_type'] == "book"  # filters


if __name__ == "__main__":
    pytest.main([__file__])
