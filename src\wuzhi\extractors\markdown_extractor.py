"""
Markdown文件内容提取器
"""

from pathlib import Path
from typing import List, Dict, Any
import re
import markdown
from markdown.extensions import toc, tables, codehilite

from .base import BaseExtractor, ExtractionResult
from ..core.models import FileType
from ..core.logger import get_logger

logger = get_logger(__name__)


class MarkdownExtractor(BaseExtractor):
    """Markdown文件提取器"""
    
    def __init__(self):
        super().__init__()
        self.supported_types = [FileType.MD]
    
    def can_extract(self, file_path: Path, file_type: FileType) -> bool:
        """检查是否可以提取指定文件"""
        return file_type in self.supported_types and file_path.exists()
    
    def extract(self, file_path: Path) -> ExtractionResult:
        """提取Markdown文件内容"""
        try:
            # 检测文件编码并读取内容
            encoding = self._detect_encoding(file_path)
            with open(file_path, 'r', encoding=encoding) as f:
                content = f.read()
            
            if not content.strip():
                return self._create_error_result("文件内容为空")
            
            # 提取Markdown结构化信息
            headings = self._extract_headings(content)
            links = self._extract_links(content)
            images = self._extract_images(content)
            code_blocks = self._extract_code_blocks(content)
            tables = self._extract_tables(content)
            
            # 转换为HTML以提取纯文本
            html_content = self._markdown_to_html(content)
            plain_text = self._html_to_text(html_content)
            
            # 提取段落
            paragraphs = self._extract_paragraphs(content)
            
            # 从内容中提取元数据
            metadata = self._extract_markdown_metadata(content)
            
            return self._create_success_result(
                text_content=content,
                plain_text=plain_text,
                title=metadata.get('title') or (headings[0] if headings else None),
                author=metadata.get('author'),
                headings=headings,
                paragraphs=paragraphs,
                links=links,
                images=images,
                tables=tables,
                metadata={
                    'extractor': 'markdown',
                    'encoding': encoding,
                    'code_blocks': code_blocks,
                    'heading_count': len(headings),
                    'link_count': len(links),
                    'image_count': len(images),
                    'table_count': len(tables),
                    **metadata
                }
            )
            
        except Exception as e:
            logger.error(f"Markdown文件提取失败 {file_path}: {e}")
            return self._create_error_result(f"提取失败: {e}")
    
    def _detect_encoding(self, file_path: Path) -> str:
        """检测文件编码"""
        try:
            import chardet
            with open(file_path, 'rb') as f:
                raw_data = f.read(10240)
            
            result = chardet.detect(raw_data)
            if result and result['confidence'] > 0.7:
                return result['encoding']
        except:
            pass
        
        # 尝试常见编码
        for encoding in ['utf-8', 'gbk', 'gb2312']:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    f.read(1024)
                return encoding
            except UnicodeDecodeError:
                continue
        
        return 'utf-8'
    
    def _extract_headings(self, content: str) -> List[str]:
        """提取Markdown标题"""
        headings = []
        
        # 匹配ATX风格标题 (# ## ###)
        atx_pattern = r'^(#{1,6})\s+(.+)$'
        
        # 匹配Setext风格标题 (=== ---)
        setext_pattern = r'^(.+)\n([=-]+)$'
        
        lines = content.split('\n')
        
        for i, line in enumerate(lines):
            # ATX风格标题
            match = re.match(atx_pattern, line.strip())
            if match:
                level = len(match.group(1))
                title = match.group(2).strip()
                headings.append(f"{'#' * level} {title}")
                continue
            
            # Setext风格标题
            if i < len(lines) - 1:
                next_line = lines[i + 1].strip()
                if next_line and all(c in '=-' for c in next_line):
                    title = line.strip()
                    if title:
                        level = 1 if next_line[0] == '=' else 2
                        headings.append(f"{'#' * level} {title}")
        
        return headings
    
    def _extract_links(self, content: str) -> List[Dict]:
        """提取Markdown链接"""
        links = []
        
        # 匹配内联链接 [text](url)
        inline_pattern = r'\[([^\]]+)\]\(([^)]+)\)'
        
        # 匹配参考链接 [text][ref]
        reference_pattern = r'\[([^\]]+)\]\[([^\]]*)\]'
        
        # 匹配链接定义 [ref]: url
        definition_pattern = r'^\[([^\]]+)\]:\s*(.+)$'
        
        # 提取内联链接
        for match in re.finditer(inline_pattern, content):
            links.append({
                'type': 'inline',
                'text': match.group(1),
                'url': match.group(2),
                'position': match.start()
            })
        
        # 提取参考链接定义
        link_definitions = {}
        for match in re.finditer(definition_pattern, content, re.MULTILINE):
            link_definitions[match.group(1)] = match.group(2)
        
        # 提取参考链接
        for match in re.finditer(reference_pattern, content):
            text = match.group(1)
            ref = match.group(2) or text  # 如果没有指定ref，使用text作为ref
            url = link_definitions.get(ref, '')
            
            links.append({
                'type': 'reference',
                'text': text,
                'ref': ref,
                'url': url,
                'position': match.start()
            })
        
        return links
    
    def _extract_images(self, content: str) -> List[Dict]:
        """提取Markdown图片"""
        images = []
        
        # 匹配图片 ![alt](src)
        image_pattern = r'!\[([^\]]*)\]\(([^)]+)\)'
        
        for match in re.finditer(image_pattern, content):
            images.append({
                'alt': match.group(1),
                'src': match.group(2),
                'position': match.start()
            })
        
        return images
    
    def _extract_code_blocks(self, content: str) -> List[Dict]:
        """提取代码块"""
        code_blocks = []
        
        # 匹配围栏代码块 ```
        fenced_pattern = r'```(\w*)\n(.*?)\n```'
        
        # 匹配缩进代码块
        indented_pattern = r'^(    .+)$'
        
        # 提取围栏代码块
        for match in re.finditer(fenced_pattern, content, re.DOTALL):
            code_blocks.append({
                'type': 'fenced',
                'language': match.group(1) or 'text',
                'code': match.group(2),
                'position': match.start()
            })
        
        return code_blocks
    
    def _extract_tables(self, content: str) -> List[Dict]:
        """提取Markdown表格"""
        tables = []
        
        lines = content.split('\n')
        i = 0
        
        while i < len(lines):
            line = lines[i].strip()
            
            # 检查是否是表格行（包含|）
            if '|' in line and i < len(lines) - 1:
                next_line = lines[i + 1].strip()
                
                # 检查下一行是否是分隔符行
                if re.match(r'^[\|\s\-:]+$', next_line):
                    # 找到表格
                    table_data = []
                    
                    # 添加表头
                    header = [cell.strip() for cell in line.split('|') if cell.strip()]
                    table_data.append(header)
                    
                    # 跳过分隔符行
                    i += 2
                    
                    # 添加表格数据行
                    while i < len(lines):
                        line = lines[i].strip()
                        if '|' in line:
                            row = [cell.strip() for cell in line.split('|') if cell.strip()]
                            table_data.append(row)
                            i += 1
                        else:
                            break
                    
                    tables.append({
                        'rows': len(table_data),
                        'columns': len(table_data[0]) if table_data else 0,
                        'data': table_data
                    })
                    
                    continue
            
            i += 1
        
        return tables
    
    def _extract_paragraphs(self, content: str) -> List[str]:
        """提取段落"""
        # 移除代码块和其他特殊块
        cleaned_content = re.sub(r'```.*?```', '', content, flags=re.DOTALL)
        cleaned_content = re.sub(r'^    .+$', '', cleaned_content, flags=re.MULTILINE)
        
        # 按空行分割段落
        paragraphs = []
        current_paragraph = []
        
        for line in cleaned_content.split('\n'):
            line = line.strip()
            
            # 跳过标题、链接定义等
            if (line.startswith('#') or 
                re.match(r'^\[.+\]:', line) or
                line.startswith('>')):
                continue
            
            if line:
                current_paragraph.append(line)
            else:
                if current_paragraph:
                    paragraph_text = ' '.join(current_paragraph)
                    if len(paragraph_text) > 20:  # 过滤太短的段落
                        paragraphs.append(paragraph_text)
                    current_paragraph = []
        
        # 添加最后一个段落
        if current_paragraph:
            paragraph_text = ' '.join(current_paragraph)
            if len(paragraph_text) > 20:
                paragraphs.append(paragraph_text)
        
        return paragraphs
    
    def _extract_markdown_metadata(self, content: str) -> Dict[str, Any]:
        """提取Markdown元数据（YAML Front Matter）"""
        metadata = {}
        
        # 检查是否有YAML Front Matter
        if content.startswith('---'):
            try:
                import yaml
                
                # 提取YAML部分
                parts = content.split('---', 2)
                if len(parts) >= 3:
                    yaml_content = parts[1].strip()
                    yaml_data = yaml.safe_load(yaml_content)
                    
                    if isinstance(yaml_data, dict):
                        metadata.update(yaml_data)
                        
            except ImportError:
                logger.debug("PyYAML未安装，跳过YAML Front Matter解析")
            except Exception as e:
                logger.debug(f"YAML Front Matter解析失败: {e}")
        
        return metadata
    
    def _markdown_to_html(self, content: str) -> str:
        """将Markdown转换为HTML"""
        try:
            md = markdown.Markdown(extensions=['toc', 'tables', 'codehilite'])
            return md.convert(content)
        except Exception as e:
            logger.debug(f"Markdown转HTML失败: {e}")
            return content
    
    def _html_to_text(self, html: str) -> str:
        """将HTML转换为纯文本"""
        try:
            # 简单的HTML标签移除
            import re
            text = re.sub(r'<[^>]+>', '', html)
            text = re.sub(r'\s+', ' ', text)
            return text.strip()
        except Exception as e:
            logger.debug(f"HTML转文本失败: {e}")
            return html
