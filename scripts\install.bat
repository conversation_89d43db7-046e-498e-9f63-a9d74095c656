@echo off
REM 悟知 (<PERSON><PERSON><PERSON>) Windows 安装脚本
setlocal enabledelayedexpansion

echo ========================================
echo     悟知 (<PERSON><PERSON><PERSON>) 安装程序
echo ========================================
echo.

REM 检查Python
echo [INFO] 检查Python安装...
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] 未找到Python，请先安装Python 3.9或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

REM 获取Python版本
for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo [INFO] Python版本: %PYTHON_VERSION%

REM 检查Python版本
python -c "import sys; exit(0 if sys.version_info >= (3, 9) else 1)" >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python版本不满足要求，需要Python 3.9或更高版本
    pause
    exit /b 1
)
echo [SUCCESS] Python版本满足要求

REM 检查Poetry
echo [INFO] 检查Poetry安装...
poetry --version >nul 2>&1
if errorlevel 1 (
    echo [WARNING] Poetry未安装，正在安装...
    call :install_poetry
    if errorlevel 1 (
        echo [ERROR] Poetry安装失败
        pause
        exit /b 1
    )
) else (
    echo [SUCCESS] Poetry已安装
)

REM 安装依赖
echo [INFO] 安装项目依赖...
poetry config virtualenvs.create true
poetry config virtualenvs.in-project true
poetry install
if errorlevel 1 (
    echo [ERROR] 依赖安装失败
    pause
    exit /b 1
)
echo [SUCCESS] 依赖安装完成

REM 初始化配置
echo [INFO] 初始化配置...
if not exist data mkdir data
if not exist logs mkdir logs
if not exist config mkdir config
if not exist cache mkdir cache
if not exist temp mkdir temp

if not exist .env (
    copy .env.example .env >nul
    echo [SUCCESS] 配置文件已创建: .env
    echo [WARNING] 请根据需要修改 .env 文件中的配置
) else (
    echo [INFO] 配置文件已存在: .env
)

REM 初始化数据库
echo [INFO] 初始化数据库...
poetry run wuzhi init --force
if errorlevel 1 (
    echo [ERROR] 数据库初始化失败
    pause
    exit /b 1
)
echo [SUCCESS] 数据库初始化完成

REM 运行测试（可选）
if "%1"=="--with-tests" (
    echo [INFO] 运行测试...
    poetry run pytest tests/ -v
    if errorlevel 1 (
        echo [WARNING] 部分测试失败，但不影响安装
    ) else (
        echo [SUCCESS] 所有测试通过
    )
)

REM 创建启动脚本
echo [INFO] 创建启动脚本...

REM 创建GUI启动脚本
echo @echo off > wuzhi-gui.bat
echo cd /d "%%~dp0" >> wuzhi-gui.bat
echo poetry run wuzhi gui >> wuzhi-gui.bat

REM 创建CLI启动脚本
echo @echo off > wuzhi-cli.bat
echo cd /d "%%~dp0" >> wuzhi-cli.bat
echo poetry run wuzhi %%* >> wuzhi-cli.bat

echo [SUCCESS] 启动脚本已创建

REM 显示完成信息
echo.
echo [SUCCESS] 悟知 (WuZhi) 安装完成！
echo.
echo 使用方法：
echo   启动图形界面: wuzhi-gui.bat
echo   使用命令行: wuzhi-cli.bat --help
echo   直接使用: poetry run wuzhi --help
echo.
echo 配置文件: .env
echo 数据目录: .\data
echo 日志目录: .\logs
echo.
echo 更多信息请查看 README.md
echo.
pause
exit /b 0

REM 安装Poetry函数
:install_poetry
echo [INFO] 下载并安装Poetry...
curl -sSL https://install.python-poetry.org | python -
if errorlevel 1 (
    echo [ERROR] Poetry下载失败，请检查网络连接
    exit /b 1
)

REM 添加Poetry到PATH（临时）
set PATH=%USERPROFILE%\.local\bin;%PATH%

REM 验证安装
poetry --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Poetry安装失败，请手动安装
    echo 安装指南: https://python-poetry.org/docs/#installation
    exit /b 1
)

echo [SUCCESS] Poetry安装成功
exit /b 0
