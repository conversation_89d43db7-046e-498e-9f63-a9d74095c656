"""
重复文档检测测试
"""

import pytest
import tempfile
from pathlib import Path
from unittest.mock import Mock, patch

from src.wuzhi.core.duplicate_detector import (
    DuplicateDetector,
    DuplicateMatch,
    DuplicateGroup,
)
from src.wuzhi.services.duplicate_service import DuplicateService
from src.wuzhi.core.models import Document


class TestDuplicateMatch:
    """重复匹配测试"""
    
    def test_duplicate_match_creation(self):
        """测试重复匹配创建"""
        match = DuplicateMatch(
            doc1_id=1,
            doc2_id=2,
            doc1_path="/path/to/doc1.txt",
            doc2_path="/path/to/doc2.txt",
            similarity_score=0.95,
            match_type="content",
            confidence=0.9
        )
        
        assert match.doc1_id == 1
        assert match.doc2_id == 2
        assert match.similarity_score == 0.95
        assert match.match_type == "content"
        assert match.confidence == 0.9
    
    def test_duplicate_match_ordering(self):
        """测试重复匹配ID排序"""
        # 测试ID自动排序
        match = DuplicateMatch(
            doc1_id=5,
            doc2_id=3,
            doc1_path="/path/to/doc5.txt",
            doc2_path="/path/to/doc3.txt",
            similarity_score=0.8,
            match_type="hash",
            confidence=1.0
        )
        
        # 应该自动调整为doc1_id <= doc2_id
        assert match.doc1_id == 3
        assert match.doc2_id == 5
        assert match.doc1_path == "/path/to/doc3.txt"
        assert match.doc2_path == "/path/to/doc5.txt"


class TestDuplicateGroup:
    """重复文档组测试"""
    
    def test_duplicate_group_creation(self):
        """测试重复文档组创建"""
        group = DuplicateGroup(
            group_id="dup_1",
            documents=[1, 2, 3],
            similarity_score=0.9,
            match_type="content",
            representative_doc=1
        )
        
        assert group.group_id == "dup_1"
        assert group.documents == [1, 2, 3]
        assert group.size() == 3
        assert group.contains(2) == True
        assert group.contains(5) == False
        assert group.representative_doc == 1


class TestDuplicateDetector:
    """重复检测器测试"""
    
    def setup_method(self):
        self.detector = DuplicateDetector()
    
    def test_clean_filename(self):
        """测试文件名清理"""
        test_cases = [
            ("document_v1.2.pdf", "document"),
            ("report_2023-12-01.docx", "report"),
            ("file_copy.txt", "file"),
            ("backup_final_draft.doc", ""),
            ("presentation_new_v2.pptx", "presentation"),
        ]
        
        for input_name, expected in test_cases:
            result = self.detector._clean_filename(input_name)
            assert result == expected, f"输入: {input_name}, 期望: {expected}, 实际: {result}"
    
    def test_group_matches_simple(self):
        """测试简单匹配分组"""
        matches = [
            DuplicateMatch(1, 2, "/doc1.txt", "/doc2.txt", 0.9, "content", 0.9),
            DuplicateMatch(2, 3, "/doc2.txt", "/doc3.txt", 0.8, "content", 0.8),
        ]
        
        groups = self.detector._group_matches(matches)
        
        assert len(groups) == 1
        assert set(groups[0].documents) == {1, 2, 3}
        assert groups[0].match_type == "content"
    
    def test_group_matches_separate_groups(self):
        """测试分离的匹配分组"""
        matches = [
            DuplicateMatch(1, 2, "/doc1.txt", "/doc2.txt", 0.9, "hash", 1.0),
            DuplicateMatch(3, 4, "/doc3.txt", "/doc4.txt", 0.8, "content", 0.8),
        ]
        
        groups = self.detector._group_matches(matches)
        
        assert len(groups) == 2
        
        # 检查组的内容
        group_docs = [set(g.documents) for g in groups]
        assert {1, 2} in group_docs
        assert {3, 4} in group_docs
    
    def test_group_matches_priority(self):
        """测试匹配类型优先级"""
        matches = [
            DuplicateMatch(1, 2, "/doc1.txt", "/doc2.txt", 1.0, "hash", 1.0),
            DuplicateMatch(1, 3, "/doc1.txt", "/doc3.txt", 0.8, "content", 0.8),
        ]
        
        groups = self.detector._group_matches(matches)
        
        assert len(groups) == 1
        assert groups[0].match_type == "hash"  # hash优先级更高
    
    @patch('src.wuzhi.core.duplicate_detector.get_db_session')
    def test_get_documents_for_detection(self, mock_session):
        """测试获取检测文档"""
        # 模拟数据库查询
        mock_query = Mock()
        mock_session.return_value.__enter__.return_value.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.all.return_value = [
            Mock(id=1, file_hash="hash1", file_path="/doc1.txt"),
            Mock(id=2, file_hash="hash2", file_path="/doc2.txt"),
        ]
        
        documents = self.detector._get_documents_for_detection([1, 2])
        
        assert len(documents) == 2
        mock_query.filter.assert_called()
    
    def test_detect_by_hash_no_duplicates(self):
        """测试哈希检测无重复"""
        documents = [
            Mock(id=1, file_hash="hash1", file_path="/doc1.txt"),
            Mock(id=2, file_hash="hash2", file_path="/doc2.txt"),
        ]
        
        matches = self.detector._detect_by_hash(documents)
        
        assert len(matches) == 0
    
    def test_detect_by_hash_with_duplicates(self):
        """测试哈希检测有重复"""
        documents = [
            Mock(id=1, file_hash="same_hash", file_path="/doc1.txt"),
            Mock(id=2, file_hash="same_hash", file_path="/doc2.txt"),
            Mock(id=3, file_hash="different_hash", file_path="/doc3.txt"),
        ]
        
        matches = self.detector._detect_by_hash(documents)
        
        assert len(matches) == 1
        assert matches[0].doc1_id == 1
        assert matches[0].doc2_id == 2
        assert matches[0].similarity_score == 1.0
        assert matches[0].match_type == "hash"
    
    def test_detect_by_filename_similarity(self):
        """测试文件名相似度检测"""
        documents = [
            Mock(id=1, file_path="/path/document_v1.pdf", file_size=1000),
            Mock(id=2, file_path="/path/document_v2.pdf", file_size=1100),
            Mock(id=3, file_path="/path/different_file.txt", file_size=500),
        ]
        
        matches = self.detector._detect_by_filename_similarity(documents)
        
        # 应该找到document_v1和document_v2的匹配
        assert len(matches) >= 0  # 可能为0，取决于文件大小相似度阈值


class TestDuplicateService:
    """重复检测服务测试"""
    
    def setup_method(self):
        self.service = DuplicateService()
    
    def test_group_to_dict(self):
        """测试重复组转字典"""
        group = DuplicateGroup(
            group_id="test_group",
            documents=[1, 2, 3],
            similarity_score=0.85,
            match_type="content",
            representative_doc=1
        )
        
        result = self.service._group_to_dict(group)
        
        expected = {
            'group_id': "test_group",
            'documents': [1, 2, 3],
            'similarity_score': 0.85,
            'match_type': "content",
            'representative_doc': 1,
            'size': 3,
        }
        
        assert result == expected
    
    @patch('src.wuzhi.services.duplicate_service.get_db_session')
    def test_get_duplicate_statistics(self, mock_session):
        """测试获取重复统计"""
        # 模拟数据库查询
        mock_session_obj = Mock()
        mock_session.return_value.__enter__.return_value = mock_session_obj
        
        # 模拟detector的统计
        with patch.object(self.service.detector, 'get_duplicate_statistics') as mock_stats:
            mock_stats.return_value = {
                'total_documents': 100,
                'duplicate_documents': 20,
                'unique_documents': 80,
                'duplicate_ratio': 0.2,
            }
            
            # 模拟重复组查询
            mock_session_obj.query.return_value.count.return_value = 5
            mock_session_obj.query.return_value.all.return_value = [
                Mock(match_type='hash'),
                Mock(match_type='content'),
                Mock(match_type='content'),
            ]
            
            stats = self.service.get_duplicate_statistics()
            
            assert stats['total_documents'] == 100
            assert stats['duplicate_documents'] == 20
            assert stats['duplicate_groups'] == 5
            assert 'groups_by_type' in stats


class TestIntegration:
    """集成测试"""
    
    def test_end_to_end_duplicate_detection(self):
        """端到端重复检测测试"""
        # 这是一个集成测试的框架
        # 在实际环境中需要真实的数据库和文档
        
        detector = DuplicateDetector()
        
        # 模拟文档数据
        mock_documents = [
            Mock(
                id=1,
                file_hash="hash1",
                file_path="/doc1.txt",
                file_size=1000
            ),
            Mock(
                id=2,
                file_hash="hash1",  # 相同哈希
                file_path="/doc1_copy.txt",
                file_size=1000
            ),
            Mock(
                id=3,
                file_hash="hash2",
                file_path="/different_doc.txt",
                file_size=2000
            ),
        ]
        
        # 测试哈希检测
        hash_matches = detector._detect_by_hash(mock_documents)
        assert len(hash_matches) == 1
        
        # 测试分组
        groups = detector._group_matches(hash_matches)
        assert len(groups) == 1
        assert set(groups[0].documents) == {1, 2}


if __name__ == "__main__":
    pytest.main([__file__])
