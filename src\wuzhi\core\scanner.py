"""
文档扫描器模块
"""

from pathlib import Path
from typing import List, Generator, Optional
from tqdm import tqdm

from .config import config
from .logger import get_logger
from .models import Document, DocumentCreate
from .database import get_db_session
from ..utils.file_utils import (
    is_supported_file,
    get_file_hash,
    get_file_size,
    get_file_type_by_content,
)

logger = get_logger(__name__)


class DocumentScanner:
    """文档扫描器"""
    
    def __init__(self):
        self.scanned_files = 0
        self.added_files = 0
        self.skipped_files = 0
        self.error_files = 0
    
    def scan_directory(
        self, 
        directory: Path, 
        recursive: bool = True,
        progress_callback: Optional[callable] = None
    ) -> List[DocumentCreate]:
        """扫描目录中的文档"""
        if not directory.exists() or not directory.is_dir():
            logger.error(f"目录不存在或不是有效目录: {directory}")
            return []
        
        logger.info(f"开始扫描目录: {directory}")
        
        # 重置计数器
        self.scanned_files = 0
        self.added_files = 0
        self.skipped_files = 0
        self.error_files = 0
        
        # 收集所有文件
        files = list(self._collect_files(directory, recursive))
        logger.info(f"发现 {len(files)} 个文件")
        
        documents = []
        
        # 使用进度条
        with tqdm(files, desc="扫描文档", unit="文件") as pbar:
            for file_path in pbar:
                try:
                    self.scanned_files += 1
                    pbar.set_postfix({
                        '已扫描': self.scanned_files,
                        '已添加': self.added_files,
                        '已跳过': self.skipped_files,
                        '错误': self.error_files
                    })
                    
                    # 检查是否为支持的文件类型
                    if not is_supported_file(file_path):
                        self.skipped_files += 1
                        logger.debug(f"跳过不支持的文件: {file_path}")
                        continue
                    
                    # 检查文件是否已存在
                    file_hash = get_file_hash(file_path)
                    if self._is_file_exists(file_hash):
                        self.skipped_files += 1
                        logger.debug(f"文件已存在，跳过: {file_path}")
                        continue
                    
                    # 创建文档记录
                    document = self._create_document_record(file_path, file_hash)
                    if document:
                        documents.append(document)
                        self.added_files += 1
                        logger.debug(f"添加文档: {file_path}")
                    else:
                        self.error_files += 1
                    
                    # 调用进度回调
                    if progress_callback:
                        progress_callback(self.scanned_files, len(files))
                
                except Exception as e:
                    self.error_files += 1
                    logger.error(f"处理文件失败 {file_path}: {e}")
        
        logger.info(f"扫描完成: 总计 {self.scanned_files} 个文件, "
                   f"添加 {self.added_files} 个, 跳过 {self.skipped_files} 个, "
                   f"错误 {self.error_files} 个")
        
        return documents
    
    def _collect_files(self, directory: Path, recursive: bool) -> Generator[Path, None, None]:
        """收集目录中的所有文件"""
        try:
            if recursive:
                # 递归扫描
                for item in directory.rglob('*'):
                    if item.is_file():
                        yield item
            else:
                # 只扫描当前目录
                for item in directory.iterdir():
                    if item.is_file():
                        yield item
        except PermissionError as e:
            logger.warning(f"权限不足，无法访问目录: {directory}")
        except Exception as e:
            logger.error(f"收集文件失败 {directory}: {e}")
    
    def _is_file_exists(self, file_hash: str) -> bool:
        """检查文件是否已存在于数据库中"""
        try:
            with get_db_session() as session:
                existing = session.query(Document).filter(
                    Document.file_hash == file_hash
                ).first()
                return existing is not None
        except Exception as e:
            logger.error(f"检查文件是否存在失败: {e}")
            return False
    
    def _create_document_record(self, file_path: Path, file_hash: str) -> Optional[DocumentCreate]:
        """创建文档记录"""
        try:
            file_type = get_file_type_by_content(file_path)
            file_size = get_file_size(file_path)
            
            document = DocumentCreate(
                file_path=str(file_path),
                file_name=file_path.name,
                file_type=file_type,
                file_size=file_size,
                file_hash=file_hash,
            )
            
            return document
            
        except Exception as e:
            logger.error(f"创建文档记录失败 {file_path}: {e}")
            return None
    
    def save_documents(self, documents: List[DocumentCreate]) -> int:
        """保存文档到数据库"""
        if not documents:
            return 0
        
        saved_count = 0
        
        try:
            with get_db_session() as session:
                for doc_create in documents:
                    try:
                        # 创建数据库记录
                        db_document = Document(
                            file_path=doc_create.file_path,
                            file_name=doc_create.file_name,
                            file_type=doc_create.file_type.value,
                            file_size=doc_create.file_size,
                            file_hash=doc_create.file_hash,
                        )
                        
                        session.add(db_document)
                        saved_count += 1
                        
                    except Exception as e:
                        logger.error(f"保存文档失败 {doc_create.file_path}: {e}")
                
                session.commit()
                logger.info(f"成功保存 {saved_count} 个文档到数据库")
                
        except Exception as e:
            logger.error(f"批量保存文档失败: {e}")
        
        return saved_count
    
    def scan_and_save(
        self, 
        directory: Path, 
        recursive: bool = True,
        progress_callback: Optional[callable] = None
    ) -> int:
        """扫描并保存文档"""
        documents = self.scan_directory(directory, recursive, progress_callback)
        return self.save_documents(documents)
    
    def get_scan_stats(self) -> dict:
        """获取扫描统计信息"""
        return {
            'scanned_files': self.scanned_files,
            'added_files': self.added_files,
            'skipped_files': self.skipped_files,
            'error_files': self.error_files,
        }


class BatchScanner:
    """批量扫描器"""
    
    def __init__(self):
        self.scanner = DocumentScanner()
        self.total_documents = 0
    
    def scan_multiple_directories(
        self, 
        directories: List[Path], 
        recursive: bool = True,
        progress_callback: Optional[callable] = None
    ) -> int:
        """扫描多个目录"""
        total_saved = 0
        
        for i, directory in enumerate(directories):
            logger.info(f"扫描目录 {i+1}/{len(directories)}: {directory}")
            
            try:
                saved = self.scanner.scan_and_save(
                    directory, 
                    recursive, 
                    progress_callback
                )
                total_saved += saved
                
            except Exception as e:
                logger.error(f"扫描目录失败 {directory}: {e}")
        
        self.total_documents = total_saved
        logger.info(f"批量扫描完成，总计保存 {total_saved} 个文档")
        
        return total_saved
    
    def get_total_stats(self) -> dict:
        """获取总体统计信息"""
        stats = self.scanner.get_scan_stats()
        stats['total_documents'] = self.total_documents
        return stats
