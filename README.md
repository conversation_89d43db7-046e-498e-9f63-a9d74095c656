# 悟知 (<PERSON><PERSON><PERSON>) - 个人知识管理系统

## 项目简介

悟知是一个智能的个人知识管理软件，旨在帮助用户高效地分析、整理和管理存储在各种终端设备上的文档资料。通过先进的文档分析技术和AI能力，为个人知识获取和管理提供极大便利。

## 主要功能

### 1. 智能文件识别
- 基于文件头标志的精准文件类型检测
- 支持多种文档格式：txt、pdf、epub、doc、docx、wps、ceb、ppt、pptx、md等
- 不依赖文件扩展名，确保识别准确性

### 2. 深度文档分析
- **文档类型识别**：自动识别书籍、报告、论文、总结等文档类型
- **元数据提取**：标题、作者、出版日期、出版社、语言等信息
- **内容统计**：页数、文件大小、字数统计
- **关键词提取**：智能提取高频关键词（最多20个）
- **智能摘要**：生成中英文摘要（不少于文档字数的1%）

### 3. 重复文档检测
- 基于内容相似度的重复文档识别
- 提供用户友好的选择性删除界面

### 4. 关键词管理
- 全局关键词频率统计
- 关键词检索和文档关联
- 降序排列显示热门关键词

## 技术特色

### AI增强功能
- **双模式摘要生成**：
  - 传统NLP算法
  - 基于Ollama Qwen3:4b大模型的智能生成
- **OCR文字识别**：当文本分析无法获取信息时，自动启用OCR识别

### 技术架构
- **后端**：Python + Rust混合开发
- **前端**：Flet跨平台应用框架
- **AI模型**：集成Ollama Qwen3:4b
- **项目管理**：Poetry依赖管理

## 快速开始

### 环境要求
- Python 3.9+
- Rust (可选，用于性能优化模块)
- Ollama (用于AI功能)

### 安装步骤

1. 克隆项目
```bash
git clone <repository-url>
cd wuzhi
```

2. 安装依赖
```bash
poetry install
```

3. 配置Ollama (可选)
```bash
# 安装Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# 下载Qwen3:4b模型
ollama pull qwen:4b
```

4. 运行应用
```bash
poetry run wuzhi
```

## 项目结构

```
wuzhi/
├── src/wuzhi/           # 主要源代码
│   ├── core/           # 核心功能模块
│   ├── analyzers/      # 文档分析器
│   ├── extractors/     # 内容提取器
│   ├── ai/            # AI集成模块
│   ├── ocr/           # OCR功能
│   ├── ui/            # 用户界面
│   └── utils/         # 工具函数
├── tests/             # 测试文件
├── docs/              # 文档
├── scripts/           # 部署脚本
└── data/              # 数据文件
```

## 开发计划

- [x] 项目初始化和架构设计
- [ ] 文件类型检测模块
- [ ] 文档内容解析模块
- [ ] 文档分析引擎
- [ ] 重复文档检测
- [ ] 关键词统计和管理
- [ ] AI集成模块
- [ ] OCR功能模块
- [ ] 前端界面开发
- [ ] 数据存储和管理
- [ ] 部署和打包

## 贡献指南

欢迎提交Issue和Pull Request来帮助改进项目。

## 许可证

[MIT License](LICENSE)
