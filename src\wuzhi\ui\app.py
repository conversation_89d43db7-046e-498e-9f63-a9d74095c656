"""
主应用界面
"""

import flet as ft
from pathlib import Path
from typing import Optional

from ..core.config import config
from ..core.logger import get_logger
from ..core.database import init_database

logger = get_logger(__name__)


class WuZhiApp:
    """悟知主应用"""
    
    def __init__(self):
        self.page: Optional[ft.Page] = None
        self.current_view = "home"
        
        # 初始化数据库
        try:
            init_database()
            logger.info("数据库初始化成功")
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
    
    def main(self, page: ft.Page) -> None:
        """主界面入口"""
        self.page = page
        self.setup_page()
        self.build_ui()
    
    def setup_page(self) -> None:
        """设置页面属性"""
        self.page.title = config.app_name
        self.page.window_width = config.window_width
        self.page.window_height = config.window_height
        self.page.window_min_width = 800
        self.page.window_min_height = 600
        
        # 设置主题
        if config.theme_mode == "dark":
            self.page.theme_mode = ft.ThemeMode.DARK
        elif config.theme_mode == "light":
            self.page.theme_mode = ft.ThemeMode.LIGHT
        else:
            self.page.theme_mode = ft.ThemeMode.SYSTEM
        
        # 设置字体
        self.page.fonts = {
            "NotoSans": "/fonts/NotoSansSC-Regular.ttf",
        }
        
        self.page.theme = ft.Theme(
            font_family="NotoSans",
        )
    
    def build_ui(self) -> None:
        """构建用户界面"""
        # 创建导航栏
        nav_rail = ft.NavigationRail(
            selected_index=0,
            label_type=ft.NavigationRailLabelType.ALL,
            min_width=100,
            min_extended_width=200,
            destinations=[
                ft.NavigationRailDestination(
                    icon=ft.icons.HOME_OUTLINED,
                    selected_icon=ft.icons.HOME,
                    label="首页",
                ),
                ft.NavigationRailDestination(
                    icon=ft.icons.FOLDER_OUTLINED,
                    selected_icon=ft.icons.FOLDER,
                    label="文档管理",
                ),
                ft.NavigationRailDestination(
                    icon=ft.icons.ANALYTICS_OUTLINED,
                    selected_icon=ft.icons.ANALYTICS,
                    label="分析结果",
                ),
                ft.NavigationRailDestination(
                    icon=ft.icons.TAG_OUTLINED,
                    selected_icon=ft.icons.TAG,
                    label="关键词",
                ),
                ft.NavigationRailDestination(
                    icon=ft.icons.COPY_OUTLINED,
                    selected_icon=ft.icons.COPY,
                    label="重复检测",
                ),
                ft.NavigationRailDestination(
                    icon=ft.icons.SETTINGS_OUTLINED,
                    selected_icon=ft.icons.SETTINGS,
                    label="设置",
                ),
            ],
            on_change=self.on_nav_change,
        )
        
        # 创建主内容区域
        self.content_area = ft.Container(
            content=self.build_home_view(),
            expand=True,
            padding=20,
        )
        
        # 创建主布局
        main_layout = ft.Row(
            [
                nav_rail,
                ft.VerticalDivider(width=1),
                self.content_area,
            ],
            expand=True,
        )
        
        self.page.add(main_layout)
        self.page.update()
    
    def on_nav_change(self, e) -> None:
        """导航栏切换事件"""
        selected_index = e.control.selected_index
        
        if selected_index == 0:
            self.current_view = "home"
            self.content_area.content = self.build_home_view()
        elif selected_index == 1:
            self.current_view = "documents"
            self.content_area.content = self.build_documents_view()
        elif selected_index == 2:
            self.current_view = "analysis"
            self.content_area.content = self.build_analysis_view()
        elif selected_index == 3:
            self.current_view = "keywords"
            self.content_area.content = self.build_keywords_view()
        elif selected_index == 4:
            self.current_view = "duplicates"
            self.content_area.content = self.build_duplicates_view()
        elif selected_index == 5:
            self.current_view = "settings"
            self.content_area.content = self.build_settings_view()
        
        self.page.update()
    
    def build_home_view(self) -> ft.Control:
        """构建首页视图"""
        return ft.Column(
            [
                ft.Text(
                    "欢迎使用悟知 (WuZhi)",
                    size=32,
                    weight=ft.FontWeight.BOLD,
                ),
                ft.Text(
                    "个人知识管理系统",
                    size=18,
                    color=ft.colors.GREY_600,
                ),
                ft.Divider(height=20),
                
                # 快速操作卡片
                ft.Row(
                    [
                        ft.Card(
                            content=ft.Container(
                                content=ft.Column(
                                    [
                                        ft.Icon(ft.icons.FOLDER_OPEN, size=40),
                                        ft.Text("扫描文档", weight=ft.FontWeight.BOLD),
                                        ft.Text("扫描指定目录中的文档文件"),
                                        ft.ElevatedButton(
                                            "开始扫描",
                                            on_click=self.on_scan_click,
                                        ),
                                    ],
                                    horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                                    spacing=10,
                                ),
                                padding=20,
                                width=200,
                            ),
                        ),
                        ft.Card(
                            content=ft.Container(
                                content=ft.Column(
                                    [
                                        ft.Icon(ft.icons.ANALYTICS, size=40),
                                        ft.Text("分析文档", weight=ft.FontWeight.BOLD),
                                        ft.Text("分析文档内容和元数据"),
                                        ft.ElevatedButton(
                                            "开始分析",
                                            on_click=self.on_analyze_click,
                                        ),
                                    ],
                                    horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                                    spacing=10,
                                ),
                                padding=20,
                                width=200,
                            ),
                        ),
                        ft.Card(
                            content=ft.Container(
                                content=ft.Column(
                                    [
                                        ft.Icon(ft.icons.SEARCH, size=40),
                                        ft.Text("检测重复", weight=ft.FontWeight.BOLD),
                                        ft.Text("查找重复的文档文件"),
                                        ft.ElevatedButton(
                                            "开始检测",
                                            on_click=self.on_duplicate_check_click,
                                        ),
                                    ],
                                    horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                                    spacing=10,
                                ),
                                padding=20,
                                width=200,
                            ),
                        ),
                    ],
                    spacing=20,
                ),
                
                ft.Divider(height=20),
                
                # 统计信息
                ft.Text("系统统计", size=20, weight=ft.FontWeight.BOLD),
                ft.Row(
                    [
                        self.build_stat_card("总文档数", "0", ft.icons.DESCRIPTION),
                        self.build_stat_card("已分析", "0", ft.icons.CHECK_CIRCLE),
                        self.build_stat_card("关键词", "0", ft.icons.TAG),
                        self.build_stat_card("重复组", "0", ft.icons.COPY),
                    ],
                    spacing=20,
                ),
            ],
            spacing=20,
        )
    
    def build_stat_card(self, title: str, value: str, icon) -> ft.Card:
        """构建统计卡片"""
        return ft.Card(
            content=ft.Container(
                content=ft.Row(
                    [
                        ft.Icon(icon, size=30, color=ft.colors.BLUE),
                        ft.Column(
                            [
                                ft.Text(value, size=24, weight=ft.FontWeight.BOLD),
                                ft.Text(title, size=12, color=ft.colors.GREY_600),
                            ],
                            spacing=0,
                        ),
                    ],
                    alignment=ft.MainAxisAlignment.START,
                    vertical_alignment=ft.CrossAxisAlignment.CENTER,
                ),
                padding=15,
                width=150,
            ),
        )
    
    def build_documents_view(self) -> ft.Control:
        """构建文档管理视图"""
        return ft.Column(
            [
                ft.Text("文档管理", size=24, weight=ft.FontWeight.BOLD),
                ft.Text("管理和查看所有文档", color=ft.colors.GREY_600),
                ft.Divider(),
                ft.Text("功能开发中..."),
            ]
        )
    
    def build_analysis_view(self) -> ft.Control:
        """构建分析结果视图"""
        return ft.Column(
            [
                ft.Text("分析结果", size=24, weight=ft.FontWeight.BOLD),
                ft.Text("查看文档分析结果", color=ft.colors.GREY_600),
                ft.Divider(),
                ft.Text("功能开发中..."),
            ]
        )
    
    def build_keywords_view(self) -> ft.Control:
        """构建关键词视图"""
        return ft.Column(
            [
                ft.Text("关键词管理", size=24, weight=ft.FontWeight.BOLD),
                ft.Text("查看和管理关键词", color=ft.colors.GREY_600),
                ft.Divider(),
                ft.Text("功能开发中..."),
            ]
        )
    
    def build_duplicates_view(self) -> ft.Control:
        """构建重复检测视图"""
        return ft.Column(
            [
                ft.Text("重复文档检测", size=24, weight=ft.FontWeight.BOLD),
                ft.Text("查找和管理重复文档", color=ft.colors.GREY_600),
                ft.Divider(),
                ft.Text("功能开发中..."),
            ]
        )
    
    def build_settings_view(self) -> ft.Control:
        """构建设置视图"""
        return ft.Column(
            [
                ft.Text("系统设置", size=24, weight=ft.FontWeight.BOLD),
                ft.Text("配置应用参数", color=ft.colors.GREY_600),
                ft.Divider(),
                ft.Text("功能开发中..."),
            ]
        )
    
    def on_scan_click(self, e) -> None:
        """扫描按钮点击事件"""
        # TODO: 实现文档扫描功能
        logger.info("用户点击扫描文档")
    
    def on_analyze_click(self, e) -> None:
        """分析按钮点击事件"""
        # TODO: 实现文档分析功能
        logger.info("用户点击分析文档")
    
    def on_duplicate_check_click(self, e) -> None:
        """重复检测按钮点击事件"""
        # TODO: 实现重复检测功能
        logger.info("用户点击检测重复")
    
    def run(self, port: int = 8080) -> None:
        """运行应用"""
        try:
            ft.app(target=self.main, port=port, view=ft.AppView.FLET_APP)
        except Exception as e:
            logger.error(f"应用运行失败: {e}")
            raise
