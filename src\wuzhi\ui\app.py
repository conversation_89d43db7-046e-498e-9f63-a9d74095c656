"""
主应用界面
"""

import flet as ft
from pathlib import Path
from typing import Optional, List, Dict
import asyncio
import threading

from ..core.config import config
from ..core.logger import get_logger
from ..core.database import init_database, get_db_session
from ..core.models import Document
from ..core.scanner import DocumentScanner, BatchScanner
from ..services.document_service import DocumentService
from ..services.duplicate_service import DuplicateService
from .components.dialogs import ScanDialog, AnalyzeDialog, ProgressDialog, SettingsDialog
from .components.widgets import DocumentCard, StatCard, SearchBar

logger = get_logger(__name__)


class WuZhiApp:
    """悟知主应用"""
    
    def __init__(self):
        self.page: Optional[ft.Page] = None
        self.current_view = "home"
        
        # 初始化数据库
        try:
            init_database()
            logger.info("数据库初始化成功")
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
    
    def main(self, page: ft.Page) -> None:
        """主界面入口"""
        self.page = page
        self.setup_page()
        self.build_ui()
    
    def setup_page(self) -> None:
        """设置页面属性"""
        self.page.title = config.app_name
        self.page.window_width = config.window_width
        self.page.window_height = config.window_height
        self.page.window_min_width = 800
        self.page.window_min_height = 600
        
        # 设置主题
        if config.theme_mode == "dark":
            self.page.theme_mode = ft.ThemeMode.DARK
        elif config.theme_mode == "light":
            self.page.theme_mode = ft.ThemeMode.LIGHT
        else:
            self.page.theme_mode = ft.ThemeMode.SYSTEM
        
        # 设置字体
        self.page.fonts = {
            "NotoSans": "/fonts/NotoSansSC-Regular.ttf",
        }
        
        self.page.theme = ft.Theme(
            font_family="NotoSans",
        )
    
    def build_ui(self) -> None:
        """构建用户界面"""
        # 创建导航栏
        nav_rail = ft.NavigationRail(
            selected_index=0,
            label_type=ft.NavigationRailLabelType.ALL,
            min_width=100,
            min_extended_width=200,
            destinations=[
                ft.NavigationRailDestination(
                    icon=ft.icons.HOME_OUTLINED,
                    selected_icon=ft.icons.HOME,
                    label="首页",
                ),
                ft.NavigationRailDestination(
                    icon=ft.icons.FOLDER_OUTLINED,
                    selected_icon=ft.icons.FOLDER,
                    label="文档管理",
                ),
                ft.NavigationRailDestination(
                    icon=ft.icons.ANALYTICS_OUTLINED,
                    selected_icon=ft.icons.ANALYTICS,
                    label="分析结果",
                ),
                ft.NavigationRailDestination(
                    icon=ft.icons.TAG_OUTLINED,
                    selected_icon=ft.icons.TAG,
                    label="关键词",
                ),
                ft.NavigationRailDestination(
                    icon=ft.icons.COPY_OUTLINED,
                    selected_icon=ft.icons.COPY,
                    label="重复检测",
                ),
                ft.NavigationRailDestination(
                    icon=ft.icons.SETTINGS_OUTLINED,
                    selected_icon=ft.icons.SETTINGS,
                    label="设置",
                ),
            ],
            on_change=self.on_nav_change,
        )
        
        # 创建主内容区域
        self.content_area = ft.Container(
            content=self.build_home_view(),
            expand=True,
            padding=20,
        )
        
        # 创建主布局
        main_layout = ft.Row(
            [
                nav_rail,
                ft.VerticalDivider(width=1),
                self.content_area,
            ],
            expand=True,
        )
        
        self.page.add(main_layout)
        self.page.update()
    
    def on_nav_change(self, e) -> None:
        """导航栏切换事件"""
        selected_index = e.control.selected_index
        
        if selected_index == 0:
            self.current_view = "home"
            self.content_area.content = self.build_home_view()
        elif selected_index == 1:
            self.current_view = "documents"
            self.content_area.content = self.build_documents_view()
        elif selected_index == 2:
            self.current_view = "analysis"
            self.content_area.content = self.build_analysis_view()
        elif selected_index == 3:
            self.current_view = "keywords"
            self.content_area.content = self.build_keywords_view()
        elif selected_index == 4:
            self.current_view = "duplicates"
            self.content_area.content = self.build_duplicates_view()
        elif selected_index == 5:
            self.current_view = "settings"
            self.content_area.content = self.build_settings_view()
        
        self.page.update()
    
    def build_home_view(self) -> ft.Control:
        """构建首页视图"""
        return ft.Column(
            [
                ft.Text(
                    "欢迎使用悟知 (WuZhi)",
                    size=32,
                    weight=ft.FontWeight.BOLD,
                ),
                ft.Text(
                    "个人知识管理系统",
                    size=18,
                    color=ft.colors.GREY_600,
                ),
                ft.Divider(height=20),
                
                # 快速操作卡片
                ft.Row(
                    [
                        ft.Card(
                            content=ft.Container(
                                content=ft.Column(
                                    [
                                        ft.Icon(ft.icons.FOLDER_OPEN, size=40),
                                        ft.Text("扫描文档", weight=ft.FontWeight.BOLD),
                                        ft.Text("扫描指定目录中的文档文件"),
                                        ft.ElevatedButton(
                                            "开始扫描",
                                            on_click=self.on_scan_click,
                                        ),
                                    ],
                                    horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                                    spacing=10,
                                ),
                                padding=20,
                                width=200,
                            ),
                        ),
                        ft.Card(
                            content=ft.Container(
                                content=ft.Column(
                                    [
                                        ft.Icon(ft.icons.ANALYTICS, size=40),
                                        ft.Text("分析文档", weight=ft.FontWeight.BOLD),
                                        ft.Text("分析文档内容和元数据"),
                                        ft.ElevatedButton(
                                            "开始分析",
                                            on_click=self.on_analyze_click,
                                        ),
                                    ],
                                    horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                                    spacing=10,
                                ),
                                padding=20,
                                width=200,
                            ),
                        ),
                        ft.Card(
                            content=ft.Container(
                                content=ft.Column(
                                    [
                                        ft.Icon(ft.icons.SEARCH, size=40),
                                        ft.Text("检测重复", weight=ft.FontWeight.BOLD),
                                        ft.Text("查找重复的文档文件"),
                                        ft.ElevatedButton(
                                            "开始检测",
                                            on_click=self.on_duplicate_check_click,
                                        ),
                                    ],
                                    horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                                    spacing=10,
                                ),
                                padding=20,
                                width=200,
                            ),
                        ),
                    ],
                    spacing=20,
                ),
                
                ft.Divider(height=20),
                
                # 统计信息
                ft.Text("系统统计", size=20, weight=ft.FontWeight.BOLD),
                ft.Row(
                    [
                        self.build_stat_card("总文档数", "0", ft.icons.DESCRIPTION),
                        self.build_stat_card("已分析", "0", ft.icons.CHECK_CIRCLE),
                        self.build_stat_card("关键词", "0", ft.icons.TAG),
                        self.build_stat_card("重复组", "0", ft.icons.COPY),
                    ],
                    spacing=20,
                ),
            ],
            spacing=20,
        )
    
    def build_stat_card(self, title: str, value: str, icon) -> ft.Card:
        """构建统计卡片"""
        return ft.Card(
            content=ft.Container(
                content=ft.Row(
                    [
                        ft.Icon(icon, size=30, color=ft.colors.BLUE),
                        ft.Column(
                            [
                                ft.Text(value, size=24, weight=ft.FontWeight.BOLD),
                                ft.Text(title, size=12, color=ft.colors.GREY_600),
                            ],
                            spacing=0,
                        ),
                    ],
                    alignment=ft.MainAxisAlignment.START,
                    vertical_alignment=ft.CrossAxisAlignment.CENTER,
                ),
                padding=15,
                width=150,
            ),
        )
    
    def build_documents_view(self) -> ft.Control:
        """构建文档管理视图"""
        return ft.Column(
            [
                ft.Text("文档管理", size=24, weight=ft.FontWeight.BOLD),
                ft.Text("管理和查看所有文档", color=ft.colors.GREY_600),
                ft.Divider(),
                ft.Text("功能开发中..."),
            ]
        )
    
    def build_analysis_view(self) -> ft.Control:
        """构建分析结果视图"""
        return ft.Column(
            [
                ft.Text("分析结果", size=24, weight=ft.FontWeight.BOLD),
                ft.Text("查看文档分析结果", color=ft.colors.GREY_600),
                ft.Divider(),
                ft.Text("功能开发中..."),
            ]
        )
    
    def build_keywords_view(self) -> ft.Control:
        """构建关键词视图"""
        return ft.Column(
            [
                ft.Text("关键词管理", size=24, weight=ft.FontWeight.BOLD),
                ft.Text("查看和管理关键词", color=ft.colors.GREY_600),
                ft.Divider(),
                ft.Text("功能开发中..."),
            ]
        )
    
    def build_duplicates_view(self) -> ft.Control:
        """构建重复检测视图"""
        return ft.Column(
            [
                ft.Text("重复文档检测", size=24, weight=ft.FontWeight.BOLD),
                ft.Text("查找和管理重复文档", color=ft.colors.GREY_600),
                ft.Divider(),
                ft.Text("功能开发中..."),
            ]
        )
    
    def build_settings_view(self) -> ft.Control:
        """构建设置视图"""
        return ft.Column(
            [
                ft.Text("系统设置", size=24, weight=ft.FontWeight.BOLD),
                ft.Text("配置应用参数", color=ft.colors.GREY_600),
                ft.Divider(),
                ft.Text("功能开发中..."),
            ]
        )
    
    def on_scan_click(self, e) -> None:
        """扫描按钮点击事件"""
        logger.info("用户点击扫描文档")

        # 显示扫描对话框
        scan_dialog = ScanDialog(self.page, self.start_scan)
        scan_dialog.show()

    def on_analyze_click(self, e) -> None:
        """分析按钮点击事件"""
        logger.info("用户点击分析文档")

        # 显示分析对话框
        analyze_dialog = AnalyzeDialog(self.page, self.start_analyze)
        analyze_dialog.show()

    def on_duplicate_check_click(self, e) -> None:
        """重复检测按钮点击事件"""
        logger.info("用户点击检测重复")

        # 直接开始重复检测
        self.start_duplicate_detection()

    def start_scan(self, paths: List[Path], recursive: bool):
        """开始文档扫描"""
        try:
            # 显示进度对话框
            progress_dialog = ProgressDialog(self.page, "扫描文档")
            progress_dialog.show()

            def scan_worker():
                try:
                    if len(paths) == 1:
                        scanner = DocumentScanner()

                        def progress_callback(current, total):
                            progress = current / total if total > 0 else 0
                            self.page.run_thread(
                                lambda: progress_dialog.update_progress(
                                    progress,
                                    f"扫描进度: {current}/{total}",
                                    f"正在扫描: {paths[0]}"
                                )
                            )

                        saved_count = scanner.scan_and_save(
                            paths[0],
                            recursive=recursive,
                            progress_callback=progress_callback
                        )

                        stats = scanner.get_scan_stats()
                    else:
                        batch_scanner = BatchScanner()

                        def progress_callback(current, total):
                            progress = current / total if total > 0 else 0
                            self.page.run_thread(
                                lambda: progress_dialog.update_progress(
                                    progress,
                                    f"批量扫描进度: {current}/{total}",
                                    "正在扫描多个目录"
                                )
                            )

                        saved_count = batch_scanner.scan_multiple_directories(
                            paths,
                            recursive=recursive,
                            progress_callback=progress_callback
                        )

                        stats = batch_scanner.get_total_stats()

                    # 扫描完成
                    self.page.run_thread(lambda: progress_dialog.close())
                    self.page.run_thread(lambda: self._show_scan_result(stats, saved_count))

                except Exception as e:
                    logger.error(f"扫描失败: {e}")
                    self.page.run_thread(lambda: progress_dialog.close())
                    self.page.run_thread(lambda: self._show_error(f"扫描失败: {e}"))

            # 在后台线程中执行扫描
            threading.Thread(target=scan_worker, daemon=True).start()

        except Exception as e:
            logger.error(f"启动扫描失败: {e}")
            self._show_error(f"启动扫描失败: {e}")

    def start_analyze(self, options: Dict):
        """开始文档分析"""
        try:
            # 显示进度对话框
            progress_dialog = ProgressDialog(self.page, "分析文档")
            progress_dialog.show()

            def analyze_worker():
                try:
                    service = DocumentService()

                    # 获取要分析的文档
                    with get_db_session() as session:
                        query = session.query(Document)

                        if not options.get('force', False):
                            query = query.filter(Document.is_analyzed == False)

                        if options.get('limit'):
                            query = query.limit(options['limit'])

                        documents = query.all()

                    if not documents:
                        self.page.run_thread(lambda: progress_dialog.close())
                        self.page.run_thread(lambda: self._show_info("没有需要分析的文档"))
                        return

                    total_docs = len(documents)
                    success_count = 0
                    error_count = 0

                    for i, doc in enumerate(documents):
                        try:
                            progress = (i + 1) / total_docs
                            self.page.run_thread(
                                lambda: progress_dialog.update_progress(
                                    progress,
                                    f"分析进度: {i+1}/{total_docs}",
                                    f"正在分析: {doc.file_name}"
                                )
                            )

                            # 执行分析
                            result = asyncio.run(service.analyze_document_async(
                                doc.id,
                                use_ai=options.get('use_ai', False),
                                force=options.get('force', False)
                            ))

                            if result['success']:
                                success_count += 1
                            else:
                                error_count += 1

                        except Exception as e:
                            logger.error(f"分析文档失败 {doc.id}: {e}")
                            error_count += 1

                    # 分析完成
                    self.page.run_thread(lambda: progress_dialog.close())
                    self.page.run_thread(lambda: self._show_analyze_result(success_count, error_count, total_docs))

                except Exception as e:
                    logger.error(f"分析失败: {e}")
                    self.page.run_thread(lambda: progress_dialog.close())
                    self.page.run_thread(lambda: self._show_error(f"分析失败: {e}"))

            # 在后台线程中执行分析
            threading.Thread(target=analyze_worker, daemon=True).start()

        except Exception as e:
            logger.error(f"启动分析失败: {e}")
            self._show_error(f"启动分析失败: {e}")

    def start_duplicate_detection(self):
        """开始重复检测"""
        try:
            # 显示进度对话框
            progress_dialog = ProgressDialog(self.page, "检测重复文档")
            progress_dialog.show()

            def detect_worker():
                try:
                    service = DuplicateService()

                    progress_dialog.update_progress(None, "正在检测重复文档...", "")

                    result = service.detect_all_duplicates()

                    # 检测完成
                    self.page.run_thread(lambda: progress_dialog.close())
                    self.page.run_thread(lambda: self._show_duplicate_result(result))

                except Exception as e:
                    logger.error(f"重复检测失败: {e}")
                    self.page.run_thread(lambda: progress_dialog.close())
                    self.page.run_thread(lambda: self._show_error(f"重复检测失败: {e}"))

            # 在后台线程中执行检测
            threading.Thread(target=detect_worker, daemon=True).start()

        except Exception as e:
            logger.error(f"启动重复检测失败: {e}")
            self._show_error(f"启动重复检测失败: {e}")
    
    def _show_scan_result(self, stats: Dict, saved_count: int):
        """显示扫描结果"""
        result_dialog = ft.AlertDialog(
            title=ft.Text("扫描完成"),
            content=ft.Column([
                ft.Text(f"扫描文件: {stats.get('scanned_files', 0)}"),
                ft.Text(f"新增文档: {stats.get('added_files', 0)}"),
                ft.Text(f"跳过文件: {stats.get('skipped_files', 0)}"),
                ft.Text(f"错误文件: {stats.get('error_files', 0)}"),
                ft.Text(f"保存到数据库: {saved_count}"),
            ], tight=True),
            actions=[ft.TextButton("确定", on_click=lambda e: self._close_dialog())],
        )
        self.page.dialog = result_dialog
        result_dialog.open = True
        self.page.update()

    def _show_analyze_result(self, success_count: int, error_count: int, total_count: int):
        """显示分析结果"""
        result_dialog = ft.AlertDialog(
            title=ft.Text("分析完成"),
            content=ft.Column([
                ft.Text(f"总文档数: {total_count}"),
                ft.Text(f"成功分析: {success_count}"),
                ft.Text(f"分析失败: {error_count}"),
                ft.Text(f"成功率: {(success_count/total_count*100):.1f}%" if total_count > 0 else "0%"),
            ], tight=True),
            actions=[ft.TextButton("确定", on_click=lambda e: self._close_dialog())],
        )
        self.page.dialog = result_dialog
        result_dialog.open = True
        self.page.update()

    def _show_duplicate_result(self, result: Dict):
        """显示重复检测结果"""
        if result['success']:
            content = ft.Column([
                ft.Text(f"发现重复组: {result['duplicate_groups']}"),
                ft.Text(f"标记重复文档: {result['marked_documents']}"),
            ], tight=True)

            if 'statistics' in result:
                stats = result['statistics']
                content.controls.extend([
                    ft.Divider(),
                    ft.Text(f"总文档数: {stats.get('total_documents', 0)}"),
                    ft.Text(f"重复文档: {stats.get('duplicate_documents', 0)}"),
                    ft.Text(f"重复率: {stats.get('duplicate_ratio', 0):.1%}"),
                ])
        else:
            content = ft.Text(f"检测失败: {result.get('error', '未知错误')}")

        result_dialog = ft.AlertDialog(
            title=ft.Text("重复检测完成"),
            content=content,
            actions=[ft.TextButton("确定", on_click=lambda e: self._close_dialog())],
        )
        self.page.dialog = result_dialog
        result_dialog.open = True
        self.page.update()

    def _show_error(self, message: str):
        """显示错误消息"""
        error_dialog = ft.AlertDialog(
            title=ft.Text("错误"),
            content=ft.Text(message),
            actions=[ft.TextButton("确定", on_click=lambda e: self._close_dialog())],
        )
        self.page.dialog = error_dialog
        error_dialog.open = True
        self.page.update()

    def _show_info(self, message: str):
        """显示信息消息"""
        info_dialog = ft.AlertDialog(
            title=ft.Text("信息"),
            content=ft.Text(message),
            actions=[ft.TextButton("确定", on_click=lambda e: self._close_dialog())],
        )
        self.page.dialog = info_dialog
        info_dialog.open = True
        self.page.update()

    def _close_dialog(self):
        """关闭对话框"""
        if self.page.dialog:
            self.page.dialog.open = False
            self.page.update()

    def run(self, port: int = 8080, host: str = "localhost") -> None:
        """运行Web应用"""
        try:
            ft.app(target=self.main, port=port, host=host, view=ft.AppView.FLET_APP)
        except Exception as e:
            logger.error(f"应用运行失败: {e}")
            raise

    def run_desktop(self) -> None:
        """运行桌面应用"""
        try:
            ft.app(target=self.main, view=ft.AppView.FLET_APP_HIDDEN)
        except Exception as e:
            logger.error(f"桌面应用运行失败: {e}")
            raise
