"""
内容提取器测试
"""

import pytest
import tempfile
from pathlib import Path

from src.wuzhi.extractors import (
    TextExtractor,
    MarkdownExtractor,
    ExtractorFactory,
    ContentExtractor,
)
from src.wuzhi.core.models import FileType


class TestTextExtractor:
    """文本提取器测试"""
    
    def setup_method(self):
        self.extractor = TextExtractor()
    
    def test_extract_simple_text(self):
        """测试简单文本提取"""
        content = "这是一个测试文本文件。\n\n包含多个段落。\n\n第三个段落。"
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
            f.write(content)
            temp_path = Path(f.name)
        
        try:
            result = self.extractor.extract(temp_path)
            
            assert result.success == True
            assert result.text_content == content
            assert len(result.paragraphs) > 0
            assert result.word_count > 0
            assert result.char_count > 0
            
        finally:
            temp_path.unlink()
    
    def test_extract_empty_file(self):
        """测试空文件提取"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write("")
            temp_path = Path(f.name)
        
        try:
            result = self.extractor.extract(temp_path)
            assert result.success == False
            assert "内容为空" in result.error_message
            
        finally:
            temp_path.unlink()
    
    def test_encoding_detection(self):
        """测试编码检测"""
        content = "这是中文内容测试"
        
        # 测试UTF-8编码
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
            f.write(content)
            temp_path = Path(f.name)
        
        try:
            result = self.extractor.extract(temp_path)
            assert result.success == True
            assert content in result.text_content
            
        finally:
            temp_path.unlink()


class TestMarkdownExtractor:
    """Markdown提取器测试"""
    
    def setup_method(self):
        self.extractor = MarkdownExtractor()
    
    def test_extract_markdown_with_headings(self):
        """测试包含标题的Markdown提取"""
        content = """# 主标题

这是第一段内容。

## 二级标题

这是第二段内容，包含**粗体**和*斜体*文本。

### 三级标题

- 列表项1
- 列表项2
- 列表项3

[链接](https://example.com)

```python
print("Hello, World!")
```
"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False, encoding='utf-8') as f:
            f.write(content)
            temp_path = Path(f.name)
        
        try:
            result = self.extractor.extract(temp_path)
            
            assert result.success == True
            assert len(result.headings) >= 3
            assert "# 主标题" in result.headings
            assert "## 二级标题" in result.headings
            assert len(result.links) >= 1
            assert len(result.paragraphs) > 0
            
        finally:
            temp_path.unlink()
    
    def test_extract_markdown_table(self):
        """测试Markdown表格提取"""
        content = """# 表格测试

| 列1 | 列2 | 列3 |
|-----|-----|-----|
| 数据1 | 数据2 | 数据3 |
| 数据4 | 数据5 | 数据6 |

这是表格后的段落。
"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False, encoding='utf-8') as f:
            f.write(content)
            temp_path = Path(f.name)
        
        try:
            result = self.extractor.extract(temp_path)
            
            assert result.success == True
            assert len(result.tables) >= 1
            assert result.tables[0]['rows'] == 3  # 包括表头
            assert result.tables[0]['columns'] == 3
            
        finally:
            temp_path.unlink()
    
    def test_extract_yaml_frontmatter(self):
        """测试YAML Front Matter提取"""
        content = """---
title: "测试文档"
author: "测试作者"
date: "2024-01-01"
---

# 文档内容

这是文档的正文内容。
"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False, encoding='utf-8') as f:
            f.write(content)
            temp_path = Path(f.name)
        
        try:
            result = self.extractor.extract(temp_path)
            
            assert result.success == True
            # 注意：需要安装PyYAML才能解析YAML Front Matter
            # 这里只测试基本功能
            assert "文档内容" in result.plain_text
            
        finally:
            temp_path.unlink()


class TestExtractorFactory:
    """提取器工厂测试"""
    
    def setup_method(self):
        self.factory = ExtractorFactory()
    
    def test_get_extractor_for_text(self):
        """测试获取文本提取器"""
        with tempfile.NamedTemporaryFile(suffix='.txt', delete=False) as f:
            temp_path = Path(f.name)
        
        try:
            extractor = self.factory.get_extractor(temp_path, FileType.TXT)
            assert extractor is not None
            assert isinstance(extractor, TextExtractor)
            
        finally:
            temp_path.unlink()
    
    def test_get_extractor_for_markdown(self):
        """测试获取Markdown提取器"""
        with tempfile.NamedTemporaryFile(suffix='.md', delete=False) as f:
            temp_path = Path(f.name)
        
        try:
            extractor = self.factory.get_extractor(temp_path, FileType.MD)
            assert extractor is not None
            assert isinstance(extractor, MarkdownExtractor)
            
        finally:
            temp_path.unlink()
    
    def test_extract_with_factory(self):
        """测试使用工厂提取内容"""
        content = "这是工厂测试内容。"
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
            f.write(content)
            temp_path = Path(f.name)
        
        try:
            result = self.factory.extract(temp_path, FileType.TXT)
            
            assert result.success == True
            assert content in result.text_content
            
        finally:
            temp_path.unlink()
    
    def test_unsupported_file_type(self):
        """测试不支持的文件类型"""
        with tempfile.NamedTemporaryFile(suffix='.unknown', delete=False) as f:
            temp_path = Path(f.name)
        
        try:
            result = self.factory.extract(temp_path, FileType.UNKNOWN)
            assert result.success == False
            assert "不支持的文件类型" in result.error_message
            
        finally:
            temp_path.unlink()
    
    def test_get_supported_types(self):
        """测试获取支持的文件类型"""
        supported_types = self.factory.get_supported_types()
        
        assert FileType.TXT in supported_types
        assert FileType.MD in supported_types
        assert FileType.PDF in supported_types
        assert FileType.DOCX in supported_types
        assert FileType.EPUB in supported_types
    
    def test_batch_extract(self):
        """测试批量提取"""
        files = []
        temp_paths = []
        
        # 创建多个测试文件
        for i in range(3):
            content = f"这是测试文件 {i+1} 的内容。"
            with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
                f.write(content)
                temp_path = Path(f.name)
                files.append(temp_path)
                temp_paths.append(temp_path)
        
        try:
            results = self.factory.batch_extract(files)
            
            assert len(results) == 3
            for path, result in results.items():
                assert result.success == True
                assert len(result.text_content) > 0
                
        finally:
            for path in temp_paths:
                path.unlink()


class TestContentExtractor:
    """内容提取器包装类测试"""
    
    def setup_method(self):
        self.extractor = ContentExtractor()
    
    def test_extract_file(self):
        """测试提取单个文件"""
        content = "这是内容提取器测试。"
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
            f.write(content)
            temp_path = Path(f.name)
        
        try:
            result = self.extractor.extract_file(temp_path)
            
            assert result.success == True
            assert content in result.text_content
            
        finally:
            temp_path.unlink()
    
    def test_is_supported(self):
        """测试文件支持检查"""
        # 创建支持的文件
        with tempfile.NamedTemporaryFile(suffix='.txt', delete=False) as f:
            temp_path = Path(f.name)
        
        try:
            assert self.extractor.is_supported(temp_path) == True
            
        finally:
            temp_path.unlink()
        
        # 测试不存在的文件
        non_existent = Path("non_existent_file.txt")
        assert self.extractor.is_supported(non_existent) == False
    
    def test_get_supported_extensions(self):
        """测试获取支持的扩展名"""
        extensions = self.extractor.get_supported_extensions()
        
        assert '.txt' in extensions
        assert '.md' in extensions
        assert '.pdf' in extensions
        assert '.docx' in extensions
        assert '.epub' in extensions
        
        # 确保返回的是排序后的列表
        assert extensions == sorted(extensions)


if __name__ == "__main__":
    pytest.main([__file__])
