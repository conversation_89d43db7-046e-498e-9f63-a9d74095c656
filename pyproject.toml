[tool.poetry]
name = "wuzhi"
version = "0.1.0"
description = "悟知 - 个人知识管理系统"
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"
license = "MIT"
homepage = "https://github.com/yourusername/wuzhi"
repository = "https://github.com/yourusername/wuzhi"
documentation = "https://github.com/yourusername/wuzhi/blob/main/README.md"
keywords = ["knowledge-management", "document-analysis", "ai", "nlp"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: End Users/Desktop",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Office/Business :: Office Suites",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Text Processing :: Linguistic",
]
packages = [{include = "wuzhi", from = "src"}]

[tool.poetry.dependencies]
python = "^3.9"
# 核心依赖
click = "^8.1.7"
pydantic = "^2.5.0"
sqlalchemy = "^2.0.23"
alembic = "^1.13.1"
python-dotenv = "^1.0.0"
loguru = "^0.7.2"

# 文档处理
pypdf2 = "^3.0.1"
python-docx = "^1.1.0"
ebooklib = "^0.18"
chardet = "^5.2.0"
python-magic = "^0.4.27"

# AI和NLP
httpx = "^0.25.2"
aiohttp = "^3.9.1"
openai = "^1.3.7"
tiktoken = "^0.5.2"

# 图形界面
flet = "^0.21.0"

# 可选依赖
pandas = {version = "^2.1.4", optional = true}
matplotlib = {version = "^3.8.2", optional = true}
seaborn = {version = "^0.13.0", optional = true}
wordcloud = {version = "^1.9.2", optional = true}
jieba = {version = "^0.42.1", optional = true}
scikit-learn = {version = "^1.3.0", optional = true}
numpy = {version = "^1.24.0", optional = true}
nltk = {version = "^3.8.1", optional = true}
spacy = {version = "^3.7.0", optional = true}

[tool.poetry.extras]
analysis = ["pandas", "matplotlib", "seaborn", "wordcloud", "jieba", "scikit-learn", "numpy"]
nlp = ["nltk", "spacy", "jieba"]
all = ["pandas", "matplotlib", "seaborn", "wordcloud", "jieba", "scikit-learn", "numpy", "nltk", "spacy"]

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.3"
pytest-asyncio = "^0.21.1"
pytest-cov = "^4.1.0"
black = "^23.11.0"
isort = "^5.12.0"
flake8 = "^6.1.0"
mypy = "^1.7.1"
pre-commit = "^3.6.0"

[tool.poetry.scripts]
wuzhi = "wuzhi.cli.main:main"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 88
target-version = ['py39']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["wuzhi"]

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "flet.*",
    "pypdf2.*",
    "docx.*",
    "ebooklib.*",
    "magic.*",
    "chardet.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/migrations/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[tool.coverage.html]
directory = "htmlcov"
