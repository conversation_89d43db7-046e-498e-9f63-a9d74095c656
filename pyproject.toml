[tool.poetry]
name = "wuzhi"
version = "0.1.0"
description = "个人知识管理软件 - Personal Knowledge Management System"
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"
packages = [{include = "wuzhi", from = "src"}]

[tool.poetry.dependencies]
python = "^3.9"
flet = "^0.21.0"
sqlalchemy = "^2.0.0"
alembic = "^1.13.0"
python-magic = "^0.4.27"
python-docx = "^1.1.0"
PyPDF2 = "^3.0.1"
ebooklib = "^0.18"
python-pptx = "^0.6.23"
markdown = "^3.5.0"
jieba = "^0.42.1"
scikit-learn = "^1.3.0"
numpy = "^1.24.0"
pandas = "^2.1.0"
requests = "^2.31.0"
aiohttp = "^3.9.0"
Pillow = "^10.1.0"
pytesseract = "^0.3.10"
opencv-python = "^4.8.0"
textstat = "^0.7.3"
langdetect = "^1.0.9"
nltk = "^3.8.1"
spacy = "^3.7.0"
transformers = "^4.35.0"
sentence-transformers = "^2.2.2"
faiss-cpu = "^1.7.4"
loguru = "^0.7.2"
pydantic = "^2.5.0"
typer = "^0.9.0"
rich = "^13.7.0"
tqdm = "^4.66.0"

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.0"
pytest-asyncio = "^0.21.0"
black = "^23.11.0"
isort = "^5.12.0"
flake8 = "^6.1.0"
mypy = "^1.7.0"
pre-commit = "^3.6.0"

[tool.poetry.scripts]
wuzhi = "wuzhi.main:app"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 88
target-version = ['py39']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["wuzhi"]

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
