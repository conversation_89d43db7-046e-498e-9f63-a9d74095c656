"""
AI集成测试
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch

from src.wuzhi.ai import (
    BaseAIProvider,
    AIResponse,
    AITaskType,
    OllamaProvider,
    AIManager,
    PromptManager,
)
from src.wuzhi.extractors.base import ExtractionResult


class MockAIProvider(BaseAIProvider):
    """模拟AI提供者"""
    
    def __init__(self, name: str = "MockAI"):
        super().__init__(name)
        self.supported_tasks = {
            AITaskType.SUMMARIZE,
            AITaskType.EXTRACT_METADATA,
            AITaskType.CLASSIFY_DOCUMENT,
            AITaskType.EXTRACT_KEYWORDS,
        }
        self.is_available = True
    
    async def initialize(self) -> bool:
        return True
    
    async def generate_text(self, prompt: str, **kwargs) -> AIResponse:
        # 模拟不同类型的响应
        if "摘要" in prompt or "summary" in prompt.lower():
            return AIResponse(
                success=True,
                content="这是一个模拟生成的摘要内容。",
                confidence=0.8,
                processing_time=1.0
            )
        elif "关键词" in prompt or "keywords" in prompt.lower():
            return AIResponse(
                success=True,
                content="人工智能\n机器学习\n深度学习\n自然语言处理",
                confidence=0.9,
                processing_time=0.8
            )
        elif "分类" in prompt or "classify" in prompt.lower():
            return AIResponse(
                success=True,
                content="类型: paper\n置信度: 0.85\n理由: 包含摘要、关键词等学术论文特征",
                confidence=0.85,
                processing_time=0.9
            )
        else:
            return AIResponse(
                success=True,
                content="模拟AI响应内容",
                confidence=0.7,
                processing_time=1.2
            )
    
    def is_task_supported(self, task_type: AITaskType) -> bool:
        return task_type in self.supported_tasks
    
    async def health_check(self) -> bool:
        return True


class TestPromptManager:
    """提示词管理器测试"""
    
    def setup_method(self):
        self.prompt_manager = PromptManager()
    
    def test_get_summarize_prompt(self):
        """测试获取摘要提示词"""
        text = "这是一个测试文档内容。"
        prompt = self.prompt_manager.get_summarize_prompt(text, max_length=100, language="zh")
        
        assert text in prompt
        assert "100" in prompt
        assert "摘要" in prompt
    
    def test_get_keyword_extraction_prompt(self):
        """测试获取关键词提取提示词"""
        text = "这是一个关于机器学习的文档。"
        prompt = self.prompt_manager.get_keyword_extraction_prompt(text, max_keywords=5)
        
        assert text in prompt
        assert "5" in prompt
        assert "关键词" in prompt
    
    def test_get_classification_prompt(self):
        """测试获取分类提示词"""
        text = "这是一篇学术论文。"
        prompt = self.prompt_manager.get_classification_prompt(text)
        
        assert text in prompt
        assert "文档类型" in prompt
    
    def test_add_custom_prompt(self):
        """测试添加自定义提示词"""
        custom_template = "自定义提示词模板: {text}"
        self.prompt_manager.add_custom_prompt("custom_task", "zh", custom_template)
        
        prompt = self.prompt_manager.get_prompt("custom_task", "zh", text="测试内容")
        assert "自定义提示词模板: 测试内容" == prompt
    
    def test_list_available_tasks(self):
        """测试列出可用任务"""
        tasks = self.prompt_manager.list_available_tasks()
        
        assert "summarize" in tasks
        assert "extract_metadata" in tasks
        assert "classify_document" in tasks
        assert "extract_keywords" in tasks


class TestAIResponse:
    """AI响应测试"""
    
    def test_ai_response_creation(self):
        """测试AI响应创建"""
        response = AIResponse(
            success=True,
            content="测试内容",
            confidence=0.8,
            processing_time=1.5
        )
        
        assert response.success == True
        assert response.content == "测试内容"
        assert response.confidence == 0.8
        assert response.processing_time == 1.5
        assert response.is_valid() == True
    
    def test_ai_response_invalid(self):
        """测试无效AI响应"""
        response = AIResponse(
            success=False,
            content="",
            error_message="测试错误"
        )
        
        assert response.success == False
        assert response.is_valid() == False
        assert response.error_message == "测试错误"
    
    def test_get_summary_dict(self):
        """测试获取摘要字典"""
        response = AIResponse(
            success=True,
            content="测试内容",
            confidence=0.8,
            processing_time=1.5,
            metadata={"key": "value"}
        )
        
        summary = response.get_summary_dict()
        
        assert summary['success'] == True
        assert summary['content_length'] == len("测试内容")
        assert summary['confidence'] == 0.8
        assert summary['processing_time'] == 1.5
        assert summary['has_metadata'] == True


class TestMockAIProvider:
    """模拟AI提供者测试"""
    
    def setup_method(self):
        self.provider = MockAIProvider()
    
    @pytest.mark.asyncio
    async def test_initialize(self):
        """测试初始化"""
        result = await self.provider.initialize()
        assert result == True
        assert self.provider.is_available == True
    
    @pytest.mark.asyncio
    async def test_health_check(self):
        """测试健康检查"""
        result = await self.provider.health_check()
        assert result == True
    
    def test_is_task_supported(self):
        """测试任务支持检查"""
        assert self.provider.is_task_supported(AITaskType.SUMMARIZE) == True
        assert self.provider.is_task_supported(AITaskType.EXTRACT_KEYWORDS) == True
        assert self.provider.is_task_supported(AITaskType.TRANSLATE) == False
    
    @pytest.mark.asyncio
    async def test_generate_summary(self):
        """测试生成摘要"""
        text = "这是一个测试文档，需要生成摘要。"
        response = await self.provider.summarize_text(text)
        
        assert response.success == True
        assert "摘要" in response.content
        assert response.confidence > 0
    
    @pytest.mark.asyncio
    async def test_extract_keywords(self):
        """测试提取关键词"""
        text = "这是一个关于人工智能和机器学习的文档。"
        response = await self.provider.extract_keywords(text)
        
        assert response.success == True
        assert "人工智能" in response.content
        assert response.confidence > 0
    
    @pytest.mark.asyncio
    async def test_classify_document(self):
        """测试文档分类"""
        text = "这是一篇学术论文，包含摘要和关键词。"
        response = await self.provider.classify_document(text)
        
        assert response.success == True
        assert "paper" in response.content
        assert response.confidence > 0


class TestAIManager:
    """AI管理器测试"""
    
    def setup_method(self):
        self.ai_manager = AIManager()
        # 清空默认提供者，使用模拟提供者
        self.ai_manager.providers.clear()
        self.ai_manager.default_provider = None
    
    def test_register_provider(self):
        """测试注册提供者"""
        provider = MockAIProvider("TestAI")
        self.ai_manager.register_provider("test", provider)
        
        assert "test" in self.ai_manager.providers
        assert self.ai_manager.providers["test"] == provider
    
    @pytest.mark.asyncio
    async def test_initialize_all(self):
        """测试初始化所有提供者"""
        provider1 = MockAIProvider("AI1")
        provider2 = MockAIProvider("AI2")
        
        self.ai_manager.register_provider("ai1", provider1)
        self.ai_manager.register_provider("ai2", provider2)
        
        results = await self.ai_manager.initialize_all()
        
        assert results["ai1"] == True
        assert results["ai2"] == True
        assert self.ai_manager.default_provider in ["ai1", "ai2"]
    
    @pytest.mark.asyncio
    async def test_get_available_provider(self):
        """测试获取可用提供者"""
        provider = MockAIProvider("TestAI")
        self.ai_manager.register_provider("test", provider)
        self.ai_manager.default_provider = "test"
        
        available_provider = await self.ai_manager.get_available_provider(AITaskType.SUMMARIZE)
        
        assert available_provider == provider
    
    @pytest.mark.asyncio
    async def test_generate_summary(self):
        """测试生成摘要"""
        provider = MockAIProvider("TestAI")
        self.ai_manager.register_provider("test", provider)
        self.ai_manager.default_provider = "test"
        
        text = "这是一个需要生成摘要的测试文档。"
        response = await self.ai_manager.generate_summary(text)
        
        assert response.success == True
        assert len(response.content) > 0
    
    @pytest.mark.asyncio
    async def test_enhance_analysis_with_ai(self):
        """测试AI增强分析"""
        provider = MockAIProvider("TestAI")
        self.ai_manager.register_provider("test", provider)
        self.ai_manager.default_provider = "test"
        
        extraction_result = ExtractionResult(
            success=True,
            plain_text="这是一个关于人工智能的学术论文，包含了机器学习和深度学习的内容。" * 10
        )
        
        results = await self.ai_manager.enhance_analysis_with_ai(extraction_result)
        
        assert "summary" in results or "keywords" in results or "classification" in results
        
        # 检查至少有一个成功的结果
        success_count = sum(1 for r in results.values() if r.success)
        assert success_count > 0
    
    def test_get_provider_status(self):
        """测试获取提供者状态"""
        provider = MockAIProvider("TestAI")
        self.ai_manager.register_provider("test", provider)
        self.ai_manager.default_provider = "test"
        
        status = self.ai_manager.get_provider_status()
        
        assert "test" in status
        assert status["test"]["available"] == True
        assert status["test"]["is_default"] == True
    
    def test_set_default_provider(self):
        """测试设置默认提供者"""
        provider = MockAIProvider("TestAI")
        self.ai_manager.register_provider("test", provider)
        
        self.ai_manager.set_default_provider("test")
        
        assert self.ai_manager.default_provider == "test"
    
    def test_is_ai_available(self):
        """测试检查AI可用性"""
        # 没有提供者时
        assert self.ai_manager.is_ai_available() == False
        
        # 添加可用提供者
        provider = MockAIProvider("TestAI")
        self.ai_manager.register_provider("test", provider)
        
        assert self.ai_manager.is_ai_available() == True


@pytest.mark.skipif(True, reason="需要实际的Ollama服务")
class TestOllamaProvider:
    """Ollama提供者测试（需要实际服务）"""
    
    def setup_method(self):
        self.provider = OllamaProvider()
    
    @pytest.mark.asyncio
    async def test_health_check(self):
        """测试健康检查"""
        result = await self.provider.health_check()
        # 这个测试需要实际的Ollama服务
        # assert result == True
    
    @pytest.mark.asyncio
    async def test_list_models(self):
        """测试列出模型"""
        models = await self.provider.list_models()
        # 这个测试需要实际的Ollama服务
        # assert isinstance(models, list)


if __name__ == "__main__":
    pytest.main([__file__])
