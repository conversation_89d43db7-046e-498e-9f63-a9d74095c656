"""
基础功能测试
"""

import pytest
from pathlib import Path
import tempfile
import os

from src.wuzhi.core.config import Config
from src.wuzhi.core.models import FileType, Language
from src.wuzhi.utils.file_utils import (
    get_file_hash,
    get_file_size,
    get_file_type_by_content,
    is_supported_file,
)
from src.wuzhi.utils.text_utils import (
    detect_language,
    extract_keywords,
    generate_summary,
    count_words,
    clean_text,
)


class TestConfig:
    """配置测试"""
    
    def test_config_creation(self):
        """测试配置创建"""
        config = Config()
        assert config.app_name == "悟知 (WuZhi)"
        assert config.version == "0.1.0"
        assert config.max_keywords == 20
    
    def test_config_directories(self):
        """测试配置目录创建"""
        config = Config()
        assert config.database_path.parent.exists()
        assert config.get_temp_dir().exists()
        assert config.get_cache_dir().exists()


class TestFileUtils:
    """文件工具测试"""
    
    def test_get_file_hash(self):
        """测试文件哈希计算"""
        with tempfile.NamedTemporaryFile(mode='w', delete=False) as f:
            f.write("test content")
            temp_path = Path(f.name)
        
        try:
            hash_value = get_file_hash(temp_path)
            assert len(hash_value) == 64  # SHA256长度
            assert hash_value != ""
        finally:
            temp_path.unlink()
    
    def test_get_file_size(self):
        """测试文件大小获取"""
        with tempfile.NamedTemporaryFile(mode='w', delete=False) as f:
            f.write("test content")
            temp_path = Path(f.name)
        
        try:
            size = get_file_size(temp_path)
            assert size > 0
        finally:
            temp_path.unlink()
    
    def test_file_type_detection(self):
        """测试文件类型检测"""
        # 测试文本文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write("This is a test text file.")
            temp_path = Path(f.name)
        
        try:
            file_type = get_file_type_by_content(temp_path)
            assert file_type == FileType.TXT
        finally:
            temp_path.unlink()
    
    def test_is_supported_file(self):
        """测试支持的文件类型检查"""
        # 创建一个小的测试文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write("test")
            temp_path = Path(f.name)
        
        try:
            assert is_supported_file(temp_path) == True
        finally:
            temp_path.unlink()


class TestTextUtils:
    """文本工具测试"""
    
    def test_detect_language(self):
        """测试语言检测"""
        chinese_text = "这是一段中文文本，用于测试语言检测功能。"
        english_text = "This is an English text for testing language detection."
        
        assert detect_language(chinese_text) == Language.CHINESE
        assert detect_language(english_text) == Language.ENGLISH
    
    def test_clean_text(self):
        """测试文本清理"""
        dirty_text = "  这是一段   包含多余空格的文本。。。  "
        cleaned = clean_text(dirty_text)
        assert cleaned == "这是一段 包含多余空格的文本."
    
    def test_count_words(self):
        """测试字数统计"""
        chinese_text = "这是中文测试文本"
        english_text = "This is English test text"
        
        zh_words, zh_chars = count_words(chinese_text, Language.CHINESE)
        en_words, en_chars = count_words(english_text, Language.ENGLISH)
        
        assert zh_words == 7  # 中文字符数
        assert en_words == 5  # 英文单词数
        assert zh_chars > 0
        assert en_chars > 0
    
    def test_extract_keywords(self):
        """测试关键词提取"""
        text = "这是一个关于机器学习和人工智能的文档。机器学习是人工智能的重要分支。"
        keywords = extract_keywords(text, max_keywords=5)
        
        assert isinstance(keywords, dict)
        assert len(keywords) <= 5
        # 应该包含高频词
        assert any("机器学习" in k or "人工智能" in k for k in keywords.keys())
    
    def test_generate_summary(self):
        """测试摘要生成"""
        text = "这是第一句话。这是第二句话，包含更多内容。这是第三句话。这是最后一句话。"
        summary = generate_summary(text, min_ratio=0.5)
        
        assert isinstance(summary, str)
        assert len(summary) > 0
        assert len(summary) < len(text)


class TestModels:
    """数据模型测试"""
    
    def test_file_type_enum(self):
        """测试文件类型枚举"""
        assert FileType.PDF == "pdf"
        assert FileType.DOCX == "docx"
        assert FileType.TXT == "txt"
    
    def test_language_enum(self):
        """测试语言枚举"""
        assert Language.CHINESE == "zh"
        assert Language.ENGLISH == "en"
        assert Language.UNKNOWN == "unknown"


if __name__ == "__main__":
    pytest.main([__file__])
