"""
Ollama AI提供者
"""

import asyncio
import json
import time
from typing import Dict, Any, Optional
import aiohttp

from .base import BaseAIProvider, AIResponse, AITaskType, AIProviderError, AIProviderNotAvailableError
from ..core.config import config
from ..core.logger import get_logger

logger = get_logger(__name__)


class OllamaProvider(BaseAIProvider):
    """Ollama AI提供者"""
    
    def __init__(self, base_url: str = None, model: str = None):
        super().__init__("Ollama")
        
        self.base_url = base_url or config.ollama_base_url
        self.model = model or config.ollama_model
        self.timeout = 60  # 默认超时60秒
        
        # Ollama支持的任务类型
        self.supported_tasks = {
            AITaskType.SUMMARIZE,
            AITaskType.EXTRACT_METADATA,
            AITaskType.CLASSIFY_DOCUMENT,
            AITaskType.EXTRACT_KEYWORDS,
            AITaskType.TRANSLATE,
            AITaskType.ANALYZE_CONTENT,
        }
        
        self.config = {
            'base_url': self.base_url,
            'model': self.model,
            'timeout': self.timeout,
        }
    
    async def initialize(self) -> bool:
        """初始化Ollama提供者"""
        try:
            # 检查Ollama服务是否可用
            if await self.health_check():
                # 检查模型是否可用
                if await self._check_model_available():
                    self.is_available = True
                    logger.info(f"Ollama提供者初始化成功: {self.model}")
                    return True
                else:
                    logger.warning(f"Ollama模型不可用: {self.model}")
                    return False
            else:
                logger.warning(f"Ollama服务不可用: {self.base_url}")
                return False
                
        except Exception as e:
            logger.error(f"Ollama提供者初始化失败: {e}")
            return False
    
    async def health_check(self) -> bool:
        """健康检查"""
        try:
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=10)) as session:
                async with session.get(f"{self.base_url}/api/tags") as response:
                    return response.status == 200
        except Exception as e:
            logger.debug(f"Ollama健康检查失败: {e}")
            return False
    
    async def _check_model_available(self) -> bool:
        """检查模型是否可用"""
        try:
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=10)) as session:
                async with session.get(f"{self.base_url}/api/tags") as response:
                    if response.status == 200:
                        data = await response.json()
                        models = data.get('models', [])
                        
                        # 检查模型是否在列表中
                        for model_info in models:
                            if model_info.get('name', '').startswith(self.model):
                                return True
                        
                        logger.warning(f"模型 {self.model} 不在可用模型列表中")
                        logger.info(f"可用模型: {[m.get('name') for m in models]}")
                        return False
                    else:
                        return False
        except Exception as e:
            logger.error(f"检查模型可用性失败: {e}")
            return False
    
    def is_task_supported(self, task_type: AITaskType) -> bool:
        """检查是否支持指定任务类型"""
        return task_type in self.supported_tasks
    
    async def generate_text(self, prompt: str, **kwargs) -> AIResponse:
        """生成文本"""
        if not self.is_available:
            return AIResponse(
                success=False,
                error_message="Ollama提供者不可用"
            )
        
        start_time = time.time()
        
        try:
            # 准备请求数据
            request_data = {
                "model": self.model,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": kwargs.get('temperature', 0.7),
                    "top_p": kwargs.get('top_p', 0.9),
                    "max_tokens": kwargs.get('max_tokens', 2048),
                }
            }
            
            # 发送请求
            async with aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=self.timeout)
            ) as session:
                async with session.post(
                    f"{self.base_url}/api/generate",
                    json=request_data,
                    headers={"Content-Type": "application/json"}
                ) as response:
                    
                    if response.status == 200:
                        data = await response.json()
                        
                        content = data.get('response', '').strip()
                        processing_time = time.time() - start_time
                        
                        if content:
                            return AIResponse(
                                success=True,
                                content=content,
                                confidence=0.8,  # Ollama默认置信度
                                processing_time=processing_time,
                                metadata={
                                    'model': self.model,
                                    'prompt_tokens': len(prompt.split()),
                                    'response_tokens': len(content.split()),
                                    'total_duration': data.get('total_duration', 0),
                                    'load_duration': data.get('load_duration', 0),
                                    'prompt_eval_duration': data.get('prompt_eval_duration', 0),
                                    'eval_duration': data.get('eval_duration', 0),
                                }
                            )
                        else:
                            return AIResponse(
                                success=False,
                                error_message="Ollama返回空响应",
                                processing_time=processing_time
                            )
                    else:
                        error_text = await response.text()
                        return AIResponse(
                            success=False,
                            error_message=f"Ollama请求失败: {response.status} - {error_text}",
                            processing_time=time.time() - start_time
                        )
                        
        except asyncio.TimeoutError:
            return AIResponse(
                success=False,
                error_message=f"Ollama请求超时 ({self.timeout}秒)",
                processing_time=time.time() - start_time
            )
        except Exception as e:
            return AIResponse(
                success=False,
                error_message=f"Ollama请求异常: {e}",
                processing_time=time.time() - start_time
            )
    
    async def pull_model(self, model_name: str = None) -> bool:
        """拉取模型"""
        model_to_pull = model_name or self.model
        
        try:
            logger.info(f"开始拉取模型: {model_to_pull}")
            
            async with aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=300)  # 5分钟超时
            ) as session:
                async with session.post(
                    f"{self.base_url}/api/pull",
                    json={"name": model_to_pull},
                    headers={"Content-Type": "application/json"}
                ) as response:
                    
                    if response.status == 200:
                        # 读取流式响应
                        async for line in response.content:
                            if line:
                                try:
                                    data = json.loads(line.decode())
                                    status = data.get('status', '')
                                    if 'downloading' in status.lower():
                                        logger.info(f"下载进度: {status}")
                                    elif 'success' in status.lower() or data.get('status') == 'success':
                                        logger.info(f"模型拉取成功: {model_to_pull}")
                                        return True
                                except json.JSONDecodeError:
                                    continue
                        
                        return True
                    else:
                        error_text = await response.text()
                        logger.error(f"模型拉取失败: {response.status} - {error_text}")
                        return False
                        
        except Exception as e:
            logger.error(f"模型拉取异常: {e}")
            return False
    
    async def list_models(self) -> list:
        """列出可用模型"""
        try:
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=10)) as session:
                async with session.get(f"{self.base_url}/api/tags") as response:
                    if response.status == 200:
                        data = await response.json()
                        models = data.get('models', [])
                        return [model.get('name', '') for model in models]
                    else:
                        return []
        except Exception as e:
            logger.error(f"获取模型列表失败: {e}")
            return []
    
    def set_model(self, model: str):
        """设置使用的模型"""
        self.model = model
        self.config['model'] = model
        logger.info(f"切换到模型: {model}")
    
    def set_timeout(self, timeout: int):
        """设置请求超时时间"""
        self.timeout = timeout
        self.config['timeout'] = timeout
    
    async def test_connection(self) -> Dict[str, Any]:
        """测试连接"""
        result = {
            'service_available': False,
            'model_available': False,
            'models': [],
            'test_generation': False,
            'error': None
        }
        
        try:
            # 测试服务可用性
            result['service_available'] = await self.health_check()
            
            if result['service_available']:
                # 获取模型列表
                result['models'] = await self.list_models()
                
                # 检查指定模型是否可用
                result['model_available'] = await self._check_model_available()
                
                if result['model_available']:
                    # 测试文本生成
                    test_response = await self.generate_text("请说'你好'")
                    result['test_generation'] = test_response.success
                    
                    if not test_response.success:
                        result['error'] = test_response.error_message
            
        except Exception as e:
            result['error'] = str(e)
        
        return result
