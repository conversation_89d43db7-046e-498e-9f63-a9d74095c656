# 悟知 (<PERSON><PERSON><PERSON>) 环境配置文件
# 复制此文件为 .env 并根据需要修改配置

# 基础配置
WUZHI_DEBUG=false
WUZHI_LOG_LEVEL=INFO

# 数据库配置
WUZHI_DATABASE_URL=sqlite:///./data/wuzhi.db

# 文件处理配置
WUZHI_MAX_FILE_SIZE=104857600  # 100MB
WUZHI_MAX_KEYWORDS=20
WUZHI_MIN_SUMMARY_RATIO=0.01
WUZHI_SIMILARITY_THRESHOLD=0.85

# AI配置
WUZHI_USE_AI_SUMMARY=true
WUZHI_OLLAMA_BASE_URL=http://localhost:11434
WUZHI_OLLAMA_MODEL=qwen:4b

# OCR配置
WUZHI_USE_OCR=true
# Windows示例: WUZHI_TESSERACT_CMD=C:\Program Files\Tesseract-OCR\tesseract.exe
# Linux/macOS示例: WUZHI_TESSERACT_CMD=/usr/bin/tesseract
WUZHI_TESSERACT_CMD=

# UI配置
WUZHI_WINDOW_WIDTH=1200
WUZHI_WINDOW_HEIGHT=800
WUZHI_THEME_MODE=system  # light, dark, system

# 日志配置
WUZHI_LOG_FILE=./data/logs/wuzhi.log
