# 悟知 (<PERSON><PERSON><PERSON>) 配置文件示例
# 复制此文件为 .env 并根据需要修改配置

# 应用基本配置
APP_NAME=悟知 (<PERSON><PERSON><PERSON>)
APP_VERSION=0.1.0
DEBUG=false
LOG_LEVEL=INFO

# 数据目录配置
DATA_DIR=./data
CONFIG_DIR=./config
CACHE_DIR=./cache
TEMP_DIR=./temp

# 数据库配置
DATABASE_URL=sqlite:///./data/wuzhi.db
DATABASE_ECHO=false

# 窗口配置
WINDOW_WIDTH=1200
WINDOW_HEIGHT=800
THEME_MODE=system

# AI配置
# Ollama配置
OLLAMA_ENABLED=true
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=qwen2.5:4b
OLLAMA_TIMEOUT=30

# OpenAI配置（可选）
OPENAI_ENABLED=false
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-3.5-turbo
OPENAI_BASE_URL=https://api.openai.com/v1

# 文档处理配置
MAX_FILE_SIZE=100MB
SUPPORTED_FORMATS=pdf,docx,doc,txt,md,epub
EXTRACT_IMAGES=false
OCR_ENABLED=false

# 分析配置
DEFAULT_LANGUAGE=zh
ENABLE_KEYWORD_EXTRACTION=true
ENABLE_SUMMARY_GENERATION=true
ENABLE_SENTIMENT_ANALYSIS=false
MIN_TEXT_LENGTH=100

# 重复检测配置
DUPLICATE_THRESHOLD=0.8
FILENAME_SIMILARITY_THRESHOLD=0.7
CONTENT_SIMILARITY_THRESHOLD=0.9

# 性能配置
MAX_WORKERS=4
BATCH_SIZE=10
CACHE_ENABLED=true
CACHE_TTL=3600

# 日志配置
LOG_FILE=./logs/wuzhi.log
LOG_ROTATION=10MB
LOG_RETENTION=30 days
LOG_FORMAT={time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} - {message}

# 网络配置
HTTP_TIMEOUT=30
MAX_RETRIES=3
USER_AGENT=WuZhi/0.1.0

# 安全配置
ENABLE_BACKUP=true
BACKUP_INTERVAL=24h
MAX_BACKUPS=7
