"""
文档扫描命令
"""

import click
from pathlib import Path
import time

from ...core.scanner import DocumentScanner, BatchScanner
from ...core.logger import get_logger

logger = get_logger(__name__)


@click.command('scan')
@click.argument('paths', nargs=-1, required=True, type=click.Path(exists=True))
@click.option('--recursive', '-r', is_flag=True, default=True, help='递归扫描子目录')
@click.option('--no-recursive', is_flag=True, help='不递归扫描子目录')
@click.option('--dry-run', is_flag=True, help='试运行（不保存到数据库）')
@click.option('--verbose', '-v', is_flag=True, help='显示详细信息')
@click.pass_context
def scan_command(ctx, paths, recursive, no_recursive, dry_run, verbose):
    """扫描文档目录
    
    PATHS: 要扫描的文件或目录路径（可以指定多个）
    """
    try:
        # 处理递归选项
        if no_recursive:
            recursive = False
        
        click.echo("开始扫描文档...")
        
        if dry_run:
            click.echo("试运行模式 - 不会保存到数据库")
        
        # 转换路径
        scan_paths = [Path(p).resolve() for p in paths]
        
        # 显示扫描信息
        click.echo(f"扫描路径: {len(scan_paths)} 个")
        for path in scan_paths:
            click.echo(f"  - {path}")
        
        click.echo(f"递归扫描: {'是' if recursive else '否'}")
        
        start_time = time.time()
        
        if len(scan_paths) == 1:
            # 单个路径扫描
            scanner = DocumentScanner()
            
            def progress_callback(current, total):
                if verbose:
                    percentage = (current / total) * 100 if total > 0 else 0
                    click.echo(f"进度: {current}/{total} ({percentage:.1f}%)")
            
            if dry_run:
                # 试运行模式
                documents = scanner.scan_directory(
                    scan_paths[0], 
                    recursive=recursive,
                    progress_callback=progress_callback if verbose else None
                )
                saved_count = len(documents)
            else:
                # 正常扫描并保存
                saved_count = scanner.scan_and_save(
                    scan_paths[0], 
                    recursive=recursive,
                    progress_callback=progress_callback if verbose else None
                )
            
            # 显示统计信息
            stats = scanner.get_scan_stats()
            
        else:
            # 多个路径批量扫描
            batch_scanner = BatchScanner()
            
            def progress_callback(current, total):
                if verbose:
                    percentage = (current / total) * 100 if total > 0 else 0
                    click.echo(f"进度: {current}/{total} ({percentage:.1f}%)")
            
            if dry_run:
                # 试运行模式 - 只扫描不保存
                total_docs = 0
                for path in scan_paths:
                    scanner = DocumentScanner()
                    documents = scanner.scan_directory(
                        path, 
                        recursive=recursive,
                        progress_callback=progress_callback if verbose else None
                    )
                    total_docs += len(documents)
                saved_count = total_docs
                stats = {'scanned_files': total_docs, 'added_files': total_docs, 'skipped_files': 0, 'error_files': 0}
            else:
                # 正常批量扫描
                saved_count = batch_scanner.scan_multiple_directories(
                    scan_paths, 
                    recursive=recursive,
                    progress_callback=progress_callback if verbose else None
                )
                stats = batch_scanner.get_total_stats()
        
        elapsed_time = time.time() - start_time
        
        # 显示结果
        click.echo("\n" + "=" * 50)
        click.echo("扫描完成！")
        click.echo("=" * 50)
        
        click.echo(f"扫描文件: {stats['scanned_files']}")
        click.echo(f"新增文档: {stats['added_files']}")
        click.echo(f"跳过文件: {stats['skipped_files']}")
        click.echo(f"错误文件: {stats['error_files']}")
        
        if not dry_run:
            click.echo(f"保存到数据库: {saved_count}")
        
        click.echo(f"耗时: {elapsed_time:.2f} 秒")
        
        if stats['error_files'] > 0:
            click.echo(f"\n警告: {stats['error_files']} 个文件处理失败")
            click.echo("请检查日志文件获取详细错误信息")
        
        # 建议下一步操作
        if not dry_run and stats['added_files'] > 0:
            click.echo("\n下一步操作:")
            click.echo("1. 分析文档: wuzhi analyze")
            click.echo("2. 检测重复: wuzhi duplicate detect")
            click.echo("3. 启动图形界面: wuzhi gui")
        
    except KeyboardInterrupt:
        click.echo("\n扫描被用户中断")
    except Exception as e:
        click.echo(f"扫描失败: {e}", err=True)
        logger.error(f"扫描失败: {e}")


@click.command('rescan')
@click.option('--all', 'rescan_all', is_flag=True, help='重新扫描所有已知路径')
@click.option('--missing', is_flag=True, help='只扫描缺失的文件')
@click.pass_context
def rescan_command(ctx, rescan_all, missing):
    """重新扫描文档"""
    try:
        if not (rescan_all or missing):
            click.echo("请指定重新扫描选项:")
            click.echo("  --all: 重新扫描所有已知路径")
            click.echo("  --missing: 只扫描缺失的文件")
            return
        
        from ...core.database import get_db_session
        from ...core.models import Document
        
        if rescan_all:
            click.echo("重新扫描所有已知路径...")
            
            # 获取所有已知的目录路径
            with get_db_session() as session:
                documents = session.query(Document).all()
                
                # 提取唯一的目录路径
                directories = set()
                for doc in documents:
                    dir_path = Path(doc.file_path).parent
                    directories.add(dir_path)
                
                click.echo(f"发现 {len(directories)} 个目录需要重新扫描")
                
                # 批量扫描
                if directories:
                    batch_scanner = BatchScanner()
                    saved_count = batch_scanner.scan_multiple_directories(list(directories))
                    
                    click.echo(f"重新扫描完成，新增 {saved_count} 个文档")
        
        elif missing:
            click.echo("检查缺失的文件...")
            
            with get_db_session() as session:
                documents = session.query(Document).all()
                
                missing_count = 0
                for doc in documents:
                    if not Path(doc.file_path).exists():
                        missing_count += 1
                        click.echo(f"缺失: {doc.file_path}")
                
                if missing_count == 0:
                    click.echo("没有发现缺失的文件")
                else:
                    click.echo(f"发现 {missing_count} 个缺失的文件")
                    
                    if click.confirm("是否从数据库中删除这些记录？"):
                        deleted_count = 0
                        for doc in documents:
                            if not Path(doc.file_path).exists():
                                session.delete(doc)
                                deleted_count += 1
                        
                        session.commit()
                        click.echo(f"删除了 {deleted_count} 个缺失文件的记录")
        
    except Exception as e:
        click.echo(f"重新扫描失败: {e}", err=True)
        logger.error(f"重新扫描失败: {e}")


# 将rescan命令添加到scan组
scan_command.add_command(rescan_command)
