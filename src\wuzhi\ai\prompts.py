"""
AI提示词管理
"""

from typing import Dict, List, Optional


class PromptManager:
    """提示词管理器"""
    
    def __init__(self):
        self.prompts = self._load_default_prompts()
    
    def _load_default_prompts(self) -> Dict[str, Dict[str, str]]:
        """加载默认提示词"""
        return {
            'summarize': {
                'zh': """请为以下文档生成一个简洁的中文摘要，摘要长度不超过{max_length}字：

文档内容：
{text}

要求：
1. 摘要应该准确概括文档的主要内容
2. 使用简洁明了的语言
3. 保持客观中性的语调
4. 突出文档的核心观点和重要信息

摘要：""",
                
                'en': """Please generate a concise English summary for the following document, with a maximum length of {max_length} words:

Document content:
{text}

Requirements:
1. The summary should accurately capture the main content of the document
2. Use clear and concise language
3. Maintain an objective and neutral tone
4. Highlight the core viewpoints and important information

Summary:"""
            },
            
            'extract_metadata': {
                'default': """请从以下文档中提取元数据信息，并以JSON格式返回：

文档内容：
{text}

请提取以下信息（如果文档中没有相关信息，请返回null）：
- title: 文档标题
- author: 作者
- publisher: 出版社
- publish_date: 出版日期
- language: 文档语言（zh/en/ja/ko等）
- document_type: 文档类型（book/paper/report/manual/article等）
- keywords: 关键词列表
- subject: 主题或学科领域

请只返回JSON格式的结果，不要包含其他解释文字："""
            },
            
            'classify_document': {
                'default': """请分析以下文档内容，判断其文档类型：

文档内容：
{text}

可能的文档类型：
- book: 书籍
- paper: 学术论文
- report: 报告
- manual: 手册/指南
- article: 文章
- summary: 总结
- presentation: 演示文稿
- other: 其他

请返回最可能的文档类型和置信度（0-1之间的数值），格式如下：
类型: [文档类型]
置信度: [数值]
理由: [简要说明判断理由]"""
            },
            
            'extract_keywords': {
                'default': """请从以下文档中提取{max_keywords}个最重要的关键词：

文档内容：
{text}

要求：
1. 关键词应该能够代表文档的核心内容
2. 优先选择专业术语和重要概念
3. 避免选择过于常见的词汇
4. 按重要性排序

请以列表形式返回关键词，每行一个："""
            },
            
            'translate': {
                'to_zh': """请将以下文本翻译成中文，保持原文的意思和语调：

原文：
{text}

中文翻译：""",
                
                'to_en': """Please translate the following text into English, maintaining the original meaning and tone:

Original text:
{text}

English translation:""",
                
                'default': """Please translate the following text into {target_language}:

Original text:
{text}

Translation:"""
            },
            
            'analyze_content': {
                'default': """请分析以下文档的内容特征：

文档内容：
{text}

请从以下几个方面进行分析：
1. 主要话题和主题
2. 文档结构和组织方式
3. 写作风格和语调
4. 目标读者群体
5. 文档质量评估

分析结果："""
            }
        }
    
    def get_summarize_prompt(self, text: str, max_length: int = 200, language: str = "zh") -> str:
        """获取摘要生成提示词"""
        template = self.prompts['summarize'].get(language, self.prompts['summarize']['zh'])
        return template.format(text=text, max_length=max_length)
    
    def get_metadata_extraction_prompt(self, text: str, document_type: str = None) -> str:
        """获取元数据提取提示词"""
        template = self.prompts['extract_metadata']['default']
        prompt = template.format(text=text)
        
        if document_type:
            prompt += f"\n\n注意：这是一个{document_type}类型的文档，请特别关注该类型文档的特有元数据。"
        
        return prompt
    
    def get_classification_prompt(self, text: str) -> str:
        """获取文档分类提示词"""
        template = self.prompts['classify_document']['default']
        return template.format(text=text)
    
    def get_keyword_extraction_prompt(self, text: str, max_keywords: int = 10) -> str:
        """获取关键词提取提示词"""
        template = self.prompts['extract_keywords']['default']
        return template.format(text=text, max_keywords=max_keywords)
    
    def get_translation_prompt(self, text: str, target_language: str = "zh") -> str:
        """获取翻译提示词"""
        if target_language == "zh":
            template = self.prompts['translate']['to_zh']
        elif target_language == "en":
            template = self.prompts['translate']['to_en']
        else:
            template = self.prompts['translate']['default']
            return template.format(text=text, target_language=target_language)
        
        return template.format(text=text)
    
    def get_content_analysis_prompt(self, text: str) -> str:
        """获取内容分析提示词"""
        template = self.prompts['analyze_content']['default']
        return template.format(text=text)
    
    def add_custom_prompt(self, task_type: str, language: str, template: str):
        """添加自定义提示词"""
        if task_type not in self.prompts:
            self.prompts[task_type] = {}
        
        self.prompts[task_type][language] = template
    
    def get_prompt(self, task_type: str, language: str = 'default', **kwargs) -> str:
        """获取指定任务和语言的提示词"""
        if task_type not in self.prompts:
            raise ValueError(f"不支持的任务类型: {task_type}")
        
        task_prompts = self.prompts[task_type]
        
        if language in task_prompts:
            template = task_prompts[language]
        elif 'default' in task_prompts:
            template = task_prompts['default']
        else:
            raise ValueError(f"任务 {task_type} 没有可用的提示词模板")
        
        try:
            return template.format(**kwargs)
        except KeyError as e:
            raise ValueError(f"提示词模板缺少必需的参数: {e}")
    
    def list_available_tasks(self) -> List[str]:
        """列出可用的任务类型"""
        return list(self.prompts.keys())
    
    def list_available_languages(self, task_type: str) -> List[str]:
        """列出指定任务的可用语言"""
        if task_type not in self.prompts:
            return []
        
        return list(self.prompts[task_type].keys())
