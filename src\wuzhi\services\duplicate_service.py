"""
重复文档检测服务
"""

from typing import Dict, List, Optional, Tuple
from pathlib import Path
from datetime import datetime

from ..core.duplicate_detector import DuplicateDetector, DuplicateGroup, DuplicateMatch
from ..core.models import Document, DuplicateGroup as DBDuplicateGroup
from ..core.database import get_db_session
from ..core.logger import get_logger

logger = get_logger(__name__)


class DuplicateService:
    """重复文档检测服务"""
    
    def __init__(self):
        self.detector = DuplicateDetector()
    
    def detect_all_duplicates(self, force_redetect: bool = False) -> Dict[str, any]:
        """检测所有文档的重复性"""
        try:
            logger.info("开始全量重复文档检测")
            
            # 如果不强制重新检测，先清除之前的标记
            if force_redetect:
                self.detector.clear_duplicate_marks()
            
            # 执行检测
            groups = self.detector.detect_duplicates()
            
            # 保存检测结果到数据库
            saved_groups = self._save_duplicate_groups(groups)
            
            # 标记重复文档
            marked_count = self.detector.mark_duplicates_in_database(groups)
            
            # 获取统计信息
            stats = self.detector.get_duplicate_statistics()
            
            result = {
                'success': True,
                'duplicate_groups': len(groups),
                'saved_groups': saved_groups,
                'marked_documents': marked_count,
                'statistics': stats,
                'detection_time': datetime.now().isoformat(),
            }
            
            logger.info(f"重复检测完成: 发现 {len(groups)} 个重复组，标记 {marked_count} 个重复文档")
            
            return result
            
        except Exception as e:
            logger.error(f"重复文档检测失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'duplicate_groups': 0,
                'marked_documents': 0,
            }
    
    def detect_document_duplicates(self, document_ids: List[int]) -> Dict[str, any]:
        """检测指定文档的重复性"""
        try:
            logger.info(f"检测指定文档的重复性: {len(document_ids)} 个文档")
            
            # 执行检测
            groups = self.detector.detect_duplicates(document_ids)
            
            # 保存检测结果
            saved_groups = self._save_duplicate_groups(groups)
            
            # 标记重复文档
            marked_count = self.detector.mark_duplicates_in_database(groups)
            
            result = {
                'success': True,
                'duplicate_groups': len(groups),
                'saved_groups': saved_groups,
                'marked_documents': marked_count,
                'groups': [self._group_to_dict(group) for group in groups],
            }
            
            return result
            
        except Exception as e:
            logger.error(f"指定文档重复检测失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'duplicate_groups': 0,
                'marked_documents': 0,
            }
    
    def get_duplicate_groups(self, limit: int = 100, offset: int = 0) -> List[Dict]:
        """获取重复文档组列表"""
        try:
            with get_db_session() as session:
                query = session.query(DBDuplicateGroup).order_by(
                    DBDuplicateGroup.similarity_score.desc()
                )
                
                if limit:
                    query = query.limit(limit)
                if offset:
                    query = query.offset(offset)
                
                db_groups = query.all()
                
                groups = []
                for db_group in db_groups:
                    group_dict = self._db_group_to_dict(db_group)
                    
                    # 获取组中的文档信息
                    doc_ids = db_group.document_ids
                    documents = session.query(Document).filter(
                        Document.id.in_(doc_ids)
                    ).all()
                    
                    group_dict['documents'] = [
                        {
                            'id': doc.id,
                            'file_name': doc.file_name,
                            'file_path': doc.file_path,
                            'file_size': doc.file_size,
                            'is_duplicate': doc.is_duplicate,
                        }
                        for doc in documents
                    ]
                    
                    groups.append(group_dict)
                
                return groups
                
        except Exception as e:
            logger.error(f"获取重复文档组失败: {e}")
            return []
    
    def get_duplicate_group_by_id(self, group_id: int) -> Optional[Dict]:
        """根据ID获取重复文档组"""
        try:
            with get_db_session() as session:
                db_group = session.query(DBDuplicateGroup).filter(
                    DBDuplicateGroup.id == group_id
                ).first()
                
                if not db_group:
                    return None
                
                group_dict = self._db_group_to_dict(db_group)
                
                # 获取组中的文档详细信息
                doc_ids = db_group.document_ids
                documents = session.query(Document).filter(
                    Document.id.in_(doc_ids)
                ).all()
                
                group_dict['documents'] = [
                    {
                        'id': doc.id,
                        'file_name': doc.file_name,
                        'file_path': doc.file_path,
                        'file_size': doc.file_size,
                        'file_type': doc.file_type,
                        'file_hash': doc.file_hash,
                        'title': doc.title,
                        'author': doc.author,
                        'is_duplicate': doc.is_duplicate,
                        'created_at': doc.created_at.isoformat() if doc.created_at else None,
                    }
                    for doc in documents
                ]
                
                return group_dict
                
        except Exception as e:
            logger.error(f"获取重复文档组失败 {group_id}: {e}")
            return None
    
    def resolve_duplicate_group(self, group_id: int, keep_document_id: int, 
                               delete_others: bool = False) -> Dict[str, any]:
        """解决重复文档组"""
        try:
            with get_db_session() as session:
                # 获取重复组
                db_group = session.query(DBDuplicateGroup).filter(
                    DBDuplicateGroup.id == group_id
                ).first()
                
                if not db_group:
                    return {'success': False, 'error': '重复组不存在'}
                
                doc_ids = db_group.document_ids
                
                if keep_document_id not in doc_ids:
                    return {'success': False, 'error': '指定的保留文档不在该重复组中'}
                
                # 更新文档状态
                updated_count = 0
                deleted_count = 0
                
                for doc_id in doc_ids:
                    doc = session.query(Document).filter(Document.id == doc_id).first()
                    if doc:
                        if doc_id == keep_document_id:
                            # 保留的文档，取消重复标记
                            doc.is_duplicate = False
                            updated_count += 1
                        else:
                            if delete_others:
                                # 删除其他文档记录
                                session.delete(doc)
                                deleted_count += 1
                            else:
                                # 保持重复标记
                                doc.is_duplicate = True
                
                # 删除重复组记录
                session.delete(db_group)
                session.commit()
                
                logger.info(f"解决重复组 {group_id}: 保留文档 {keep_document_id}, "
                           f"更新 {updated_count} 个文档, 删除 {deleted_count} 个文档")
                
                return {
                    'success': True,
                    'kept_document': keep_document_id,
                    'updated_documents': updated_count,
                    'deleted_documents': deleted_count,
                }
                
        except Exception as e:
            logger.error(f"解决重复文档组失败 {group_id}: {e}")
            return {'success': False, 'error': str(e)}
    
    def ignore_duplicate_group(self, group_id: int) -> Dict[str, any]:
        """忽略重复文档组"""
        try:
            with get_db_session() as session:
                # 获取重复组
                db_group = session.query(DBDuplicateGroup).filter(
                    DBDuplicateGroup.id == group_id
                ).first()
                
                if not db_group:
                    return {'success': False, 'error': '重复组不存在'}
                
                doc_ids = db_group.document_ids
                
                # 取消所有文档的重复标记
                updated_count = 0
                for doc_id in doc_ids:
                    doc = session.query(Document).filter(Document.id == doc_id).first()
                    if doc and doc.is_duplicate:
                        doc.is_duplicate = False
                        updated_count += 1
                
                # 删除重复组记录
                session.delete(db_group)
                session.commit()
                
                logger.info(f"忽略重复组 {group_id}: 取消 {updated_count} 个文档的重复标记")
                
                return {
                    'success': True,
                    'updated_documents': updated_count,
                }
                
        except Exception as e:
            logger.error(f"忽略重复文档组失败 {group_id}: {e}")
            return {'success': False, 'error': str(e)}
    
    def get_duplicate_statistics(self) -> Dict[str, any]:
        """获取重复文档统计信息"""
        try:
            stats = self.detector.get_duplicate_statistics()
            
            # 获取重复组统计
            with get_db_session() as session:
                group_count = session.query(DBDuplicateGroup).count()
                
                # 按匹配类型统计
                type_stats = {}
                groups = session.query(DBDuplicateGroup).all()
                for group in groups:
                    match_type = getattr(group, 'match_type', 'unknown')
                    type_stats[match_type] = type_stats.get(match_type, 0) + 1
                
                stats.update({
                    'duplicate_groups': group_count,
                    'groups_by_type': type_stats,
                })
            
            return stats
            
        except Exception as e:
            logger.error(f"获取重复文档统计失败: {e}")
            return {}
    
    def clear_all_duplicates(self) -> Dict[str, any]:
        """清除所有重复标记和组"""
        try:
            with get_db_session() as session:
                # 清除重复标记
                marked_count = self.detector.clear_duplicate_marks()
                
                # 删除所有重复组
                group_count = session.query(DBDuplicateGroup).count()
                session.query(DBDuplicateGroup).delete()
                session.commit()
                
                logger.info(f"清除所有重复数据: {marked_count} 个标记, {group_count} 个组")
                
                return {
                    'success': True,
                    'cleared_marks': marked_count,
                    'deleted_groups': group_count,
                }
                
        except Exception as e:
            logger.error(f"清除重复数据失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def _save_duplicate_groups(self, groups: List[DuplicateGroup]) -> int:
        """保存重复文档组到数据库"""
        saved_count = 0
        
        try:
            with get_db_session() as session:
                for group in groups:
                    # 检查是否已存在相同的组
                    existing = session.query(DBDuplicateGroup).filter(
                        DBDuplicateGroup.group_hash == group.group_id
                    ).first()
                    
                    if not existing:
                        db_group = DBDuplicateGroup(
                            group_hash=group.group_id,
                            similarity_score=group.similarity_score,
                            document_ids=group.documents,
                        )
                        session.add(db_group)
                        saved_count += 1
                
                session.commit()
                
        except Exception as e:
            logger.error(f"保存重复文档组失败: {e}")
        
        return saved_count
    
    def _group_to_dict(self, group: DuplicateGroup) -> Dict:
        """将重复组转换为字典"""
        return {
            'group_id': group.group_id,
            'documents': group.documents,
            'similarity_score': group.similarity_score,
            'match_type': group.match_type,
            'representative_doc': group.representative_doc,
            'size': group.size(),
        }
    
    def _db_group_to_dict(self, db_group: DBDuplicateGroup) -> Dict:
        """将数据库重复组转换为字典"""
        return {
            'id': db_group.id,
            'group_hash': db_group.group_hash,
            'similarity_score': db_group.similarity_score,
            'document_ids': db_group.document_ids,
            'created_at': db_group.created_at.isoformat() if db_group.created_at else None,
        }
