"""
对话框组件
"""

import flet as ft
from pathlib import Path
from typing import Callable, Optional, List
import asyncio

from ...core.logger import get_logger

logger = get_logger(__name__)


class ScanDialog:
    """文档扫描对话框"""
    
    def __init__(self, page: ft.Page, on_scan: Callable = None):
        self.page = page
        self.on_scan = on_scan
        self.dialog = None
        self.selected_paths = []
        
        self.path_list = ft.ListView(
            height=200,
            spacing=5,
        )
        
        self.recursive_checkbox = ft.Checkbox(
            label="递归扫描子目录",
            value=True,
        )
        
        self.build_dialog()
    
    def build_dialog(self):
        """构建对话框"""
        self.dialog = ft.AlertDialog(
            modal=True,
            title=ft.Text("扫描文档"),
            content=ft.Container(
                content=ft.Column(
                    [
                        ft.Text("选择要扫描的目录:"),
                        ft.Row(
                            [
                                ft.ElevatedButton(
                                    "添加目录",
                                    icon=ft.icons.FOLDER_OPEN,
                                    on_click=self.pick_directory,
                                ),
                                ft.ElevatedButton(
                                    "清空列表",
                                    icon=ft.icons.CLEAR,
                                    on_click=self.clear_paths,
                                ),
                            ],
                            spacing=10,
                        ),
                        self.path_list,
                        self.recursive_checkbox,
                    ],
                    spacing=10,
                    tight=True,
                ),
                width=500,
                height=350,
            ),
            actions=[
                ft.TextButton("取消", on_click=self.close_dialog),
                ft.ElevatedButton(
                    "开始扫描",
                    icon=ft.icons.PLAY_ARROW,
                    on_click=self.start_scan,
                ),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )
    
    def pick_directory(self, e):
        """选择目录"""
        def on_result(result: ft.FilePickerResultEvent):
            if result.path:
                path = Path(result.path)
                if path not in self.selected_paths:
                    self.selected_paths.append(path)
                    self.update_path_list()
        
        file_picker = ft.FilePicker(on_result=on_result)
        self.page.overlay.append(file_picker)
        self.page.update()
        
        file_picker.get_directory_path(dialog_title="选择扫描目录")
    
    def clear_paths(self, e):
        """清空路径列表"""
        self.selected_paths.clear()
        self.update_path_list()
    
    def update_path_list(self):
        """更新路径列表显示"""
        self.path_list.controls.clear()
        
        for path in self.selected_paths:
            self.path_list.controls.append(
                ft.ListTile(
                    leading=ft.Icon(ft.icons.FOLDER),
                    title=ft.Text(str(path)),
                    trailing=ft.IconButton(
                        icon=ft.icons.DELETE,
                        on_click=lambda e, p=path: self.remove_path(p),
                    ),
                )
            )
        
        self.page.update()
    
    def remove_path(self, path: Path):
        """移除路径"""
        if path in self.selected_paths:
            self.selected_paths.remove(path)
            self.update_path_list()
    
    def start_scan(self, e):
        """开始扫描"""
        if not self.selected_paths:
            self.show_error("请至少选择一个目录")
            return
        
        if self.on_scan:
            self.on_scan(self.selected_paths, self.recursive_checkbox.value)
        
        self.close_dialog(e)
    
    def show_error(self, message: str):
        """显示错误消息"""
        error_dialog = ft.AlertDialog(
            title=ft.Text("错误"),
            content=ft.Text(message),
            actions=[ft.TextButton("确定", on_click=lambda e: self.page.dialog.close())],
        )
        self.page.dialog = error_dialog
        error_dialog.open = True
        self.page.update()
    
    def close_dialog(self, e):
        """关闭对话框"""
        self.dialog.open = False
        self.page.update()
    
    def show(self):
        """显示对话框"""
        self.page.dialog = self.dialog
        self.dialog.open = True
        self.page.update()


class AnalyzeDialog:
    """文档分析对话框"""
    
    def __init__(self, page: ft.Page, on_analyze: Callable = None):
        self.page = page
        self.on_analyze = on_analyze
        self.dialog = None
        
        self.analyze_all_checkbox = ft.Checkbox(
            label="分析所有未分析的文档",
            value=True,
        )
        
        self.force_checkbox = ft.Checkbox(
            label="强制重新分析已分析的文档",
            value=False,
        )
        
        self.ai_checkbox = ft.Checkbox(
            label="启用AI增强分析",
            value=True,
        )
        
        self.limit_field = ft.TextField(
            label="限制分析数量（可选）",
            hint_text="留空表示不限制",
            width=200,
        )
        
        self.build_dialog()
    
    def build_dialog(self):
        """构建对话框"""
        self.dialog = ft.AlertDialog(
            modal=True,
            title=ft.Text("分析文档"),
            content=ft.Container(
                content=ft.Column(
                    [
                        ft.Text("分析选项:"),
                        self.analyze_all_checkbox,
                        self.force_checkbox,
                        self.ai_checkbox,
                        self.limit_field,
                        ft.Divider(),
                        ft.Text(
                            "注意: AI分析需要配置AI提供者（如Ollama）",
                            size=12,
                            color=ft.colors.GREY_600,
                        ),
                    ],
                    spacing=10,
                    tight=True,
                ),
                width=400,
                height=250,
            ),
            actions=[
                ft.TextButton("取消", on_click=self.close_dialog),
                ft.ElevatedButton(
                    "开始分析",
                    icon=ft.icons.ANALYTICS,
                    on_click=self.start_analyze,
                ),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )
    
    def start_analyze(self, e):
        """开始分析"""
        options = {
            'analyze_all': self.analyze_all_checkbox.value,
            'force': self.force_checkbox.value,
            'use_ai': self.ai_checkbox.value,
            'limit': None,
        }
        
        # 处理限制数量
        if self.limit_field.value and self.limit_field.value.strip():
            try:
                options['limit'] = int(self.limit_field.value.strip())
            except ValueError:
                self.show_error("限制数量必须是有效的整数")
                return
        
        if self.on_analyze:
            self.on_analyze(options)
        
        self.close_dialog(e)
    
    def show_error(self, message: str):
        """显示错误消息"""
        error_dialog = ft.AlertDialog(
            title=ft.Text("错误"),
            content=ft.Text(message),
            actions=[ft.TextButton("确定", on_click=lambda e: self.page.dialog.close())],
        )
        self.page.dialog = error_dialog
        error_dialog.open = True
        self.page.update()
    
    def close_dialog(self, e):
        """关闭对话框"""
        self.dialog.open = False
        self.page.update()
    
    def show(self):
        """显示对话框"""
        self.page.dialog = self.dialog
        self.dialog.open = True
        self.page.update()


class ProgressDialog:
    """进度对话框"""
    
    def __init__(self, page: ft.Page, title: str = "处理中"):
        self.page = page
        self.title = title
        self.dialog = None
        self.is_cancellable = False
        self.on_cancel = None
        
        self.progress_bar = ft.ProgressBar(width=400)
        self.status_text = ft.Text("正在处理...")
        self.detail_text = ft.Text("", size=12, color=ft.colors.GREY_600)
        
        self.build_dialog()
    
    def build_dialog(self):
        """构建对话框"""
        content_controls = [
            self.status_text,
            self.progress_bar,
            self.detail_text,
        ]
        
        actions = []
        if self.is_cancellable:
            actions.append(
                ft.TextButton("取消", on_click=self.cancel_operation)
            )
        
        self.dialog = ft.AlertDialog(
            modal=True,
            title=ft.Text(self.title),
            content=ft.Container(
                content=ft.Column(
                    content_controls,
                    spacing=10,
                    tight=True,
                ),
                width=450,
                height=120,
            ),
            actions=actions,
            actions_alignment=ft.MainAxisAlignment.END,
        )
    
    def update_progress(self, progress: float, status: str = None, detail: str = None):
        """更新进度"""
        if 0 <= progress <= 1:
            self.progress_bar.value = progress
        else:
            self.progress_bar.value = None  # 不确定进度
        
        if status:
            self.status_text.value = status
        
        if detail:
            self.detail_text.value = detail
        
        self.page.update()
    
    def set_cancellable(self, cancellable: bool, on_cancel: Callable = None):
        """设置是否可取消"""
        self.is_cancellable = cancellable
        self.on_cancel = on_cancel
        self.build_dialog()
    
    def cancel_operation(self, e):
        """取消操作"""
        if self.on_cancel:
            self.on_cancel()
        self.close()
    
    def close(self):
        """关闭对话框"""
        self.dialog.open = False
        self.page.update()
    
    def show(self):
        """显示对话框"""
        self.page.dialog = self.dialog
        self.dialog.open = True
        self.page.update()


class SettingsDialog:
    """设置对话框"""
    
    def __init__(self, page: ft.Page, on_save: Callable = None):
        self.page = page
        self.on_save = on_save
        self.dialog = None
        
        # 创建设置控件
        self.theme_dropdown = ft.Dropdown(
            label="主题模式",
            options=[
                ft.dropdown.Option("system", "跟随系统"),
                ft.dropdown.Option("light", "浅色模式"),
                ft.dropdown.Option("dark", "深色模式"),
            ],
            value="system",
            width=200,
        )
        
        self.ai_enabled_checkbox = ft.Checkbox(
            label="启用AI功能",
            value=True,
        )
        
        self.ollama_url_field = ft.TextField(
            label="Ollama服务地址",
            value="http://localhost:11434",
            width=300,
        )
        
        self.ollama_model_field = ft.TextField(
            label="Ollama模型",
            value="qwen2.5:4b",
            width=200,
        )
        
        self.build_dialog()
    
    def build_dialog(self):
        """构建对话框"""
        self.dialog = ft.AlertDialog(
            modal=True,
            title=ft.Text("系统设置"),
            content=ft.Container(
                content=ft.Column(
                    [
                        ft.Text("界面设置", weight=ft.FontWeight.BOLD),
                        self.theme_dropdown,
                        ft.Divider(),
                        
                        ft.Text("AI设置", weight=ft.FontWeight.BOLD),
                        self.ai_enabled_checkbox,
                        self.ollama_url_field,
                        self.ollama_model_field,
                        
                        ft.Text(
                            "注意: 修改AI设置后需要重启应用",
                            size=12,
                            color=ft.colors.GREY_600,
                        ),
                    ],
                    spacing=10,
                    tight=True,
                ),
                width=400,
                height=350,
            ),
            actions=[
                ft.TextButton("取消", on_click=self.close_dialog),
                ft.ElevatedButton(
                    "保存",
                    icon=ft.icons.SAVE,
                    on_click=self.save_settings,
                ),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )
    
    def save_settings(self, e):
        """保存设置"""
        settings = {
            'theme_mode': self.theme_dropdown.value,
            'ai_enabled': self.ai_enabled_checkbox.value,
            'ollama_url': self.ollama_url_field.value,
            'ollama_model': self.ollama_model_field.value,
        }
        
        if self.on_save:
            self.on_save(settings)
        
        self.close_dialog(e)
    
    def close_dialog(self, e):
        """关闭对话框"""
        self.dialog.open = False
        self.page.update()
    
    def show(self):
        """显示对话框"""
        self.page.dialog = self.dialog
        self.dialog.open = True
        self.page.update()
