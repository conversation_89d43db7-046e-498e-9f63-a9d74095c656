"""
文档分析器测试
"""

import pytest
import tempfile
from pathlib import Path

from src.wuzhi.analyzers import (
    DocumentTypeAnalyzer,
    MetadataAnalyzer,
    ContentAnalyzer,
    AnalyzerEngine,
)
from src.wuzhi.extractors.base import ExtractionResult
from src.wuzhi.core.models import DocumentType, Language


class TestDocumentTypeAnalyzer:
    """文档类型分析器测试"""
    
    def setup_method(self):
        self.analyzer = DocumentTypeAnalyzer()
    
    def test_analyze_book_content(self):
        """测试书籍内容分析"""
        book_content = """
        目录
        
        第一章 引言
        本书主要介绍...
        
        第二章 基础知识
        在这一章中，我们将学习...
        
        第三章 高级技巧
        进阶内容包括...
        
        参考文献
        [1] 某某著作，某某出版社，2023年
        """
        
        extraction_result = ExtractionResult(
            success=True,
            plain_text=book_content,
            headings=["第一章 引言", "第二章 基础知识", "第三章 高级技巧"]
        )
        
        result = self.analyzer.analyze(extraction_result)
        
        assert result.success == True
        assert result.document_type == DocumentType.BOOK
        assert result.document_type_confidence > 0.5
    
    def test_analyze_paper_content(self):
        """测试论文内容分析"""
        paper_content = """
        摘要
        本文研究了机器学习在自然语言处理中的应用...
        
        关键词：机器学习，自然语言处理，深度学习
        
        1. 引言
        随着人工智能技术的发展...
        
        2. 相关工作
        在这个领域中，已有的研究包括...
        
        3. 方法
        我们提出的方法基于...
        
        4. 实验结果
        实验表明...
        
        5. 结论
        本文的主要贡献是...
        
        参考文献
        [1] Smith, J. et al. "Machine Learning Advances", 2023.
        """
        
        extraction_result = ExtractionResult(
            success=True,
            plain_text=paper_content,
            headings=["摘要", "1. 引言", "2. 相关工作", "3. 方法", "4. 实验结果", "5. 结论"]
        )
        
        result = self.analyzer.analyze(extraction_result)
        
        assert result.success == True
        assert result.document_type == DocumentType.PAPER
        assert result.document_type_confidence > 0.6
    
    def test_analyze_manual_content(self):
        """测试手册内容分析"""
        manual_content = """
        用户手册
        
        快速入门指南
        
        第1步：安装软件
        请按照以下步骤安装...
        
        第2步：配置设置
        在配置页面中...
        
        第3步：开始使用
        现在您可以开始使用软件...
        
        常见问题
        Q: 如何重置密码？
        A: 请联系管理员...
        """
        
        extraction_result = ExtractionResult(
            success=True,
            plain_text=manual_content,
            headings=["用户手册", "快速入门指南", "第1步：安装软件", "第2步：配置设置"]
        )
        
        result = self.analyzer.analyze(extraction_result)
        
        assert result.success == True
        assert result.document_type == DocumentType.MANUAL
        assert result.document_type_confidence > 0.5


class TestMetadataAnalyzer:
    """元数据分析器测试"""
    
    def setup_method(self):
        self.analyzer = MetadataAnalyzer()
    
    def test_extract_title_and_author(self):
        """测试标题和作者提取"""
        content = """
        机器学习实战指南
        
        作者：张三
        出版社：科技出版社
        出版日期：2023年6月
        
        本书是一本关于机器学习的实用指南...
        """
        
        extraction_result = ExtractionResult(
            success=True,
            plain_text=content,
            headings=["机器学习实战指南"]
        )
        
        result = self.analyzer.analyze(extraction_result)
        
        assert result.success == True
        assert "机器学习实战指南" in result.title
        assert result.title_confidence > 0.7
        assert result.author == "张三"
        assert result.author_confidence > 0.6
        assert result.publisher == "科技出版社"
        assert "2023" in result.publish_date
    
    def test_extract_english_metadata(self):
        """测试英文元数据提取"""
        content = """
        Advanced Machine Learning Techniques
        
        Author: John Smith
        Publisher: Tech Press
        Date: 2023-06-15
        
        This book provides comprehensive coverage of...
        """
        
        extraction_result = ExtractionResult(
            success=True,
            plain_text=content,
            headings=["Advanced Machine Learning Techniques"]
        )
        
        result = self.analyzer.analyze(extraction_result)
        
        assert result.success == True
        assert "Advanced Machine Learning Techniques" in result.title
        assert result.author == "John Smith"
        assert result.publisher == "Tech Press"
        assert "2023" in result.publish_date
    
    def test_language_detection(self):
        """测试语言检测"""
        chinese_content = "这是一篇中文文档，主要介绍了人工智能的发展历程和未来趋势。"
        english_content = "This is an English document about artificial intelligence development."
        
        # 测试中文
        extraction_result = ExtractionResult(
            success=True,
            plain_text=chinese_content
        )
        
        result = self.analyzer.analyze(extraction_result)
        assert result.success == True
        assert result.language == Language.CHINESE
        
        # 测试英文
        extraction_result = ExtractionResult(
            success=True,
            plain_text=english_content
        )
        
        result = self.analyzer.analyze(extraction_result)
        assert result.success == True
        assert result.language == Language.ENGLISH


class TestContentAnalyzer:
    """内容分析器测试"""
    
    def setup_method(self):
        self.analyzer = ContentAnalyzer()
    
    def test_keyword_extraction(self):
        """测试关键词提取"""
        content = """
        人工智能是计算机科学的一个分支，它试图理解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。
        机器学习是人工智能的一个重要分支，通过算法使机器能够从数据中学习。
        深度学习是机器学习的一个子领域，它基于人工神经网络的研究。
        自然语言处理是人工智能的另一个重要应用领域。
        """
        
        extraction_result = ExtractionResult(
            success=True,
            plain_text=content
        )
        
        result = self.analyzer.analyze(extraction_result)
        
        assert result.success == True
        assert len(result.keywords) > 0
        assert "人工智能" in result.keywords
        assert "机器学习" in result.keywords
        assert result.word_count > 0
        assert result.char_count > 0
    
    def test_summary_generation(self):
        """测试摘要生成"""
        content = """
        人工智能（Artificial Intelligence，AI）是计算机科学的一个分支。
        它试图理解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。
        该领域的研究包括机器人、语言识别、图像识别、自然语言处理和专家系统等。
        
        机器学习是人工智能的核心技术之一。
        它是一种通过算法使机器能够从数据中学习的方法。
        机器学习算法通过训练数据来构建数学模型，以便对新数据进行预测或决策。
        
        深度学习是机器学习的一个子领域。
        它基于人工神经网络的研究，特别是利用多层神经网络来进行学习和表示。
        深度学习在图像识别、语音识别、自然语言处理等领域取得了显著成果。
        """ * 3  # 重复内容以确保有足够长度
        
        extraction_result = ExtractionResult(
            success=True,
            plain_text=content
        )
        
        result = self.analyzer.analyze(extraction_result)
        
        assert result.success == True
        assert result.summary is not None
        assert len(result.summary) > 50  # 摘要应该有一定长度
        assert len(result.summary) < len(content)  # 摘要应该比原文短
        assert result.text_quality > 0
    
    def test_main_topics_extraction(self):
        """测试主要话题提取"""
        content = "人工智能和机器学习是现代科技的重要组成部分。深度学习技术推动了计算机视觉的发展。"
        
        extraction_result = ExtractionResult(
            success=True,
            plain_text=content,
            headings=["人工智能概述", "机器学习基础"]
        )
        
        result = self.analyzer.analyze(extraction_result)
        
        assert result.success == True
        assert len(result.main_topics) > 0
        # 主要话题应该包含关键词或标题
        topics_text = ' '.join(result.main_topics)
        assert any(keyword in topics_text for keyword in ["人工智能", "机器学习", "概述", "基础"])


class TestAnalyzerEngine:
    """分析引擎测试"""
    
    def setup_method(self):
        self.engine = AnalyzerEngine()
    
    def test_complete_analysis(self):
        """测试完整分析流程"""
        content = """
        深度学习实战教程
        
        作者：李四
        出版社：AI出版社
        出版日期：2023年8月
        
        第一章 深度学习基础
        深度学习是机器学习的一个重要分支，它模拟人脑神经网络的结构和功能。
        
        第二章 神经网络架构
        本章介绍各种神经网络架构，包括卷积神经网络、循环神经网络等。
        
        第三章 实战项目
        通过实际项目来学习深度学习的应用。
        """
        
        extraction_result = ExtractionResult(
            success=True,
            plain_text=content,
            headings=["深度学习实战教程", "第一章 深度学习基础", "第二章 神经网络架构", "第三章 实战项目"],
            page_count=150
        )
        
        result = self.engine.analyze_document(extraction_result)
        
        assert result.success == True
        assert result.document_type is not None
        assert result.title is not None
        assert result.author == "李四"
        assert result.publisher == "AI出版社"
        assert len(result.keywords) > 0
        assert result.summary is not None
        assert result.page_count == 150
        assert result.get_overall_confidence() > 0.5
    
    def test_batch_analysis(self):
        """测试批量分析"""
        # 创建多个提取结果
        extraction_results = {}
        
        for i in range(3):
            content = f"""
            测试文档 {i+1}
            
            这是第 {i+1} 个测试文档的内容。
            文档包含了关于测试的相关信息。
            """
            
            extraction_result = ExtractionResult(
                success=True,
                plain_text=content,
                headings=[f"测试文档 {i+1}"]
            )
            
            file_path = Path(f"test_doc_{i+1}.txt")
            extraction_results[file_path] = extraction_result
        
        # 批量分析
        results = self.engine.batch_analyze(extraction_results)
        
        assert len(results) == 3
        for file_path, result in results.items():
            assert result.success == True
            assert result.title is not None
    
    def test_analysis_statistics(self):
        """测试分析统计"""
        # 创建一些分析结果
        from src.wuzhi.analyzers.base import AnalysisResult
        
        results = {
            Path("doc1.txt"): AnalysisResult(
                success=True,
                document_type=DocumentType.BOOK,
                language=Language.CHINESE,
                confidence=0.8,
                text_quality=0.7
            ),
            Path("doc2.txt"): AnalysisResult(
                success=True,
                document_type=DocumentType.PAPER,
                language=Language.ENGLISH,
                confidence=0.9,
                text_quality=0.8
            ),
            Path("doc3.txt"): AnalysisResult(
                success=False,
                error_message="分析失败"
            ),
        }
        
        stats = self.engine.get_analysis_statistics(results)
        
        assert stats['total_documents'] == 3
        assert stats['successful_analyses'] == 2
        assert stats['success_rate'] == 2/3
        assert 'book' in stats['document_type_distribution']
        assert 'paper' in stats['document_type_distribution']
        assert 'zh' in stats['language_distribution']
        assert 'en' in stats['language_distribution']
        assert stats['average_confidence'] > 0


if __name__ == "__main__":
    pytest.main([__file__])
