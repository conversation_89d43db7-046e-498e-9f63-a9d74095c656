"""
内容分析器
"""

from pathlib import Path
from typing import Dict, List, Tuple

from .base import BaseAnalyzer, AnalysisResult
from ..core.config import config
from ..core.logger import get_logger
from ..extractors.base import ExtractionResult
from ..utils.text_utils import (
    extract_keywords,
    generate_summary,
    count_words,
    detect_language,
)

logger = get_logger(__name__)


class ContentAnalyzer(BaseAnalyzer):
    """内容分析器"""
    
    def __init__(self):
        super().__init__()
    
    def analyze(self, extraction_result: ExtractionResult, file_path: Path = None) -> AnalysisResult:
        """分析文档内容"""
        if not self.can_analyze(extraction_result):
            return self._create_error_result("无法分析内容：内容为空或提取失败")
        
        try:
            text = extraction_result.plain_text
            
            # 检测语言
            language = detect_language(text)
            
            # 统计字数
            word_count, char_count = count_words(text, language)
            
            # 提取关键词
            keywords = self._extract_keywords(text)
            
            # 生成摘要
            summary = self._generate_summary(text, language)
            summary_zh = self._generate_chinese_summary(text, summary, language)
            
            # 提取主要话题
            main_topics = self._extract_main_topics(keywords, extraction_result.headings)
            
            # 计算文本质量
            text_quality = self._calculate_text_quality(text)
            
            # 计算置信度
            confidence = self._calculate_content_confidence(
                keywords, summary, word_count, text_quality
            )
            
            return self._create_success_result(
                keywords=keywords,
                summary=summary,
                summary_zh=summary_zh,
                word_count=word_count,
                char_count=char_count,
                language=language,
                main_topics=main_topics,
                text_quality=text_quality,
                confidence=confidence,
                metadata={
                    'keyword_count': len(keywords),
                    'summary_length': len(summary) if summary else 0,
                    'summary_zh_length': len(summary_zh) if summary_zh else 0,
                    'analysis_method': 'nlp_analysis',
                }
            )
            
        except Exception as e:
            logger.error(f"内容分析失败: {e}")
            return self._create_error_result(f"分析失败: {e}")
    
    def _extract_keywords(self, text: str) -> Dict[str, int]:
        """提取关键词"""
        try:
            # 使用配置的最大关键词数量
            max_keywords = config.max_keywords
            
            # 提取关键词
            keywords = extract_keywords(text, max_keywords=max_keywords, use_tfidf=True)
            
            # 过滤和清理关键词
            filtered_keywords = self._filter_keywords(keywords)
            
            return filtered_keywords
            
        except Exception as e:
            logger.error(f"关键词提取失败: {e}")
            return {}
    
    def _filter_keywords(self, keywords: Dict[str, int]) -> Dict[str, int]:
        """过滤和清理关键词"""
        filtered = {}
        
        # 过滤条件
        min_length = 2  # 最小长度
        max_length = 20  # 最大长度
        min_frequency = 1  # 最小频率
        
        # 停用词（补充）
        stop_words = {
            '可以', '应该', '需要', '必须', '能够', '可能', '或者', '但是', '然后',
            '因为', '所以', '如果', '虽然', '不过', '而且', '并且', '以及', '还有',
            'can', 'should', 'need', 'must', 'able', 'may', 'or', 'but', 'then',
            'because', 'so', 'if', 'although', 'however', 'and', 'also', 'with'
        }
        
        for keyword, frequency in keywords.items():
            # 长度过滤
            if not (min_length <= len(keyword) <= max_length):
                continue
            
            # 频率过滤
            if frequency < min_frequency:
                continue
            
            # 停用词过滤
            if keyword.lower() in stop_words:
                continue
            
            # 数字过滤（纯数字关键词通常意义不大）
            if keyword.isdigit():
                continue
            
            # 特殊字符过滤
            if not any(c.isalnum() or '\u4e00' <= c <= '\u9fff' for c in keyword):
                continue
            
            filtered[keyword] = frequency
        
        # 按频率排序并限制数量
        sorted_keywords = dict(sorted(filtered.items(), key=lambda x: x[1], reverse=True))
        return dict(list(sorted_keywords.items())[:config.max_keywords])
    
    def _generate_summary(self, text: str, language) -> str:
        """生成摘要"""
        try:
            # 计算目标摘要长度
            word_count, _ = count_words(text, language)
            min_ratio = config.min_summary_ratio
            
            # 生成摘要
            summary = generate_summary(text, min_ratio=min_ratio)
            
            # 确保摘要不为空且有意义
            if summary and len(summary.strip()) > 20:
                return summary.strip()
            
            # 如果自动生成的摘要太短，尝试提取前几句
            return self._extract_first_sentences(text, target_length=max(100, word_count // 100))
            
        except Exception as e:
            logger.error(f"摘要生成失败: {e}")
            return ""
    
    def _generate_chinese_summary(self, text: str, summary: str, language) -> str:
        """生成中文摘要"""
        try:
            # 如果原文就是中文，直接返回摘要
            if language and language.value == 'zh':
                return summary
            
            # 如果摘要为空，返回空字符串
            if not summary:
                return ""
            
            # TODO: 这里可以集成翻译服务
            # 目前简单返回原摘要，后续可以添加翻译功能
            logger.debug("中文摘要翻译功能待实现")
            return summary
            
        except Exception as e:
            logger.error(f"中文摘要生成失败: {e}")
            return ""
    
    def _extract_first_sentences(self, text: str, target_length: int = 200) -> str:
        """提取前几句作为摘要"""
        import re
        
        # 分句
        sentences = re.split(r'[.。!！?？]', text)
        
        summary_parts = []
        current_length = 0
        
        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue
            
            if current_length + len(sentence) > target_length and summary_parts:
                break
            
            summary_parts.append(sentence)
            current_length += len(sentence)
        
        if summary_parts:
            return '。'.join(summary_parts) + '。'
        
        # 如果没有找到合适的句子，返回前target_length个字符
        return text[:target_length] + "..." if len(text) > target_length else text
    
    def _extract_main_topics(self, keywords: Dict[str, int], headings: List[str]) -> List[str]:
        """提取主要话题"""
        topics = []
        
        # 从关键词中提取高频话题
        if keywords:
            # 取前5个高频关键词作为主要话题
            top_keywords = list(keywords.keys())[:5]
            topics.extend(top_keywords)
        
        # 从标题中提取话题
        if headings:
            for heading in headings[:3]:  # 只取前3个标题
                # 清理标题格式
                clean_heading = heading.strip().lstrip('#').strip()
                if clean_heading and len(clean_heading) < 50:
                    topics.append(clean_heading)
        
        # 去重并限制数量
        unique_topics = []
        for topic in topics:
            if topic not in unique_topics:
                unique_topics.append(topic)
        
        return unique_topics[:10]  # 最多返回10个主要话题
    
    def _calculate_content_confidence(self, keywords: Dict[str, int], summary: str, 
                                    word_count: int, text_quality: float) -> float:
        """计算内容分析置信度"""
        confidence = 0.0
        
        # 关键词质量评分 (0.3权重)
        if keywords:
            keyword_score = min(len(keywords) / 10, 1.0)  # 10个关键词为满分
            confidence += keyword_score * 0.3
        
        # 摘要质量评分 (0.2权重)
        if summary:
            summary_score = min(len(summary) / 200, 1.0)  # 200字符为满分
            confidence += summary_score * 0.2
        
        # 文本长度评分 (0.2权重)
        if word_count:
            length_score = min(word_count / 1000, 1.0)  # 1000字为满分
            confidence += length_score * 0.2
        
        # 文本质量评分 (0.3权重)
        confidence += text_quality * 0.3
        
        return min(confidence, 1.0)
    
    def get_content_statistics(self, analysis_result: AnalysisResult) -> Dict[str, any]:
        """获取内容统计信息"""
        return {
            'word_count': analysis_result.word_count,
            'char_count': analysis_result.char_count,
            'keyword_count': len(analysis_result.keywords),
            'summary_length': len(analysis_result.summary) if analysis_result.summary else 0,
            'language': analysis_result.language.value if analysis_result.language else None,
            'text_quality': analysis_result.text_quality,
            'main_topics_count': len(analysis_result.main_topics),
            'confidence': analysis_result.confidence,
        }
