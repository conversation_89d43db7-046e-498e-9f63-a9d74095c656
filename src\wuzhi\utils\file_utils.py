"""
文件处理工具函数
"""

import hashlib
import magic
from pathlib import Path
from typing import Optional

from ..core.config import config
from ..core.logger import get_logger
from ..core.models import FileType
from ..core.file_detector import file_detector

logger = get_logger(__name__)

# 文件头标志映射
FILE_SIGNATURES = {
    # PDF
    b'%PDF': FileType.PDF,

    # Microsoft Office (OLE2 格式)
    b'\xd0\xcf\x11\xe0\xa1\xb1\x1a\xe1': FileType.DOC,  # DOC/PPT/XLS

    # Microsoft Office (ZIP 格式) - 需要进一步检查内容
    b'PK\x03\x04': 'ZIP_BASED',  # 可能是DOCX/PPTX/XLSX/EPUB

    # EPUB (特殊的ZIP格式)
    b'PK\x03\x04\x14\x00\x06\x00': FileType.EPUB,

    # WPS文件 (类似OLE2)
    b'\xd0\xcf\x11\xe0': FileType.WPS,

    # CEB文件 (方正Apabi格式)
    b'CEB\x00': FileType.CEB,
    b'\x43\x45\x42': FileType.CEB,

    # 文本文件 (UTF-8 BOM)
    b'\xef\xbb\xbf': FileType.TXT,

    # 文本文件 (UTF-16 BOM)
    b'\xff\xfe': FileType.TXT,
    b'\xfe\xff': FileType.TXT,
}

# MIME类型映射
MIME_TYPE_MAP = {
    'application/pdf': FileType.PDF,
    'application/epub+zip': FileType.EPUB,
    'application/msword': FileType.DOC,
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': FileType.DOCX,
    'application/vnd.ms-powerpoint': FileType.PPT,
    'application/vnd.openxmlformats-officedocument.presentationml.presentation': FileType.PPTX,
    'text/plain': FileType.TXT,
    'text/markdown': FileType.MD,
}


def get_file_hash(file_path: Path, algorithm: str = 'sha256') -> str:
    """计算文件哈希值"""
    try:
        hash_obj = hashlib.new(algorithm)
        with open(file_path, 'rb') as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_obj.update(chunk)
        return hash_obj.hexdigest()
    except Exception as e:
        logger.error(f"计算文件哈希失败 {file_path}: {e}")
        return ""


def get_file_size(file_path: Path) -> int:
    """获取文件大小（字节）"""
    try:
        return file_path.stat().st_size
    except Exception as e:
        logger.error(f"获取文件大小失败 {file_path}: {e}")
        return 0


def is_supported_file(file_path: Path) -> bool:
    """检查文件是否为支持的类型"""
    return file_detector.is_supported_file(file_path)


def _detect_zip_based_format(file_path: Path) -> FileType:
    """检测基于ZIP的文件格式"""
    try:
        import zipfile

        with zipfile.ZipFile(file_path, 'r') as zip_file:
            file_list = zip_file.namelist()

            # 检查EPUB格式
            if 'META-INF/container.xml' in file_list and 'mimetype' in file_list:
                return FileType.EPUB

            # 检查DOCX格式
            if 'word/document.xml' in file_list:
                return FileType.DOCX

            # 检查PPTX格式
            if 'ppt/presentation.xml' in file_list or any('ppt/slides/' in f for f in file_list):
                return FileType.PPTX

            # 检查其他Office格式的特征文件
            if '[Content_Types].xml' in file_list:
                # 读取Content_Types.xml来确定具体类型
                try:
                    content_types = zip_file.read('[Content_Types].xml').decode('utf-8')
                    if 'wordprocessingml' in content_types:
                        return FileType.DOCX
                    elif 'presentationml' in content_types:
                        return FileType.PPTX
                except:
                    pass

        return FileType.UNKNOWN

    except zipfile.BadZipFile:
        return FileType.UNKNOWN
    except Exception as e:
        logger.debug(f"ZIP格式检测失败 {file_path}: {e}")
        return FileType.UNKNOWN


def get_file_type_by_content(file_path: Path) -> FileType:
    """通过文件内容检测文件类型"""
    return file_detector.detect_file_type(file_path)


def is_office_document(file_path: Path) -> bool:
    """检查是否为Office文档"""
    file_type = get_file_type_by_content(file_path)
    return file_type in [FileType.DOC, FileType.DOCX, FileType.PPT, FileType.PPTX]


def is_zip_based_office(file_path: Path) -> bool:
    """检查是否为基于ZIP的Office文档（DOCX, PPTX等）"""
    try:
        with open(file_path, 'rb') as f:
            header = f.read(4)
        return header == b'PK\x03\x04'
    except Exception:
        return False


def get_file_info(file_path: Path) -> dict:
    """获取文件基本信息"""
    info = file_detector.get_file_info(file_path)
    if info:
        info['hash'] = get_file_hash(file_path)
    return info


def validate_file_path(file_path: str) -> Optional[Path]:
    """验证文件路径"""
    try:
        path = Path(file_path)
        if not path.exists():
            logger.warning(f"文件不存在: {file_path}")
            return None
        
        if not path.is_file():
            logger.warning(f"不是文件: {file_path}")
            return None
        
        return path
    except Exception as e:
        logger.error(f"文件路径验证失败 {file_path}: {e}")
        return None


def ensure_directory(directory: Path) -> bool:
    """确保目录存在"""
    try:
        directory.mkdir(parents=True, exist_ok=True)
        return True
    except Exception as e:
        logger.error(f"创建目录失败 {directory}: {e}")
        return False


def get_relative_path(file_path: Path, base_path: Path) -> str:
    """获取相对路径"""
    try:
        return str(file_path.relative_to(base_path))
    except ValueError:
        return str(file_path)
