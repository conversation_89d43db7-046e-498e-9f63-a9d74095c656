"""
命令行主入口
"""

import click
import asyncio
from pathlib import Path

from ..core.config import config
from ..core.logger import get_logger
from .commands import (
    init_command,
    scan_command,
    analyze_command,
    duplicate_command,
    gui_command,
    status_command,
    export_command,
)

logger = get_logger(__name__)


@click.group()
@click.version_option(version=config.version, prog_name=config.app_name)
@click.option('--debug', is_flag=True, help='启用调试模式')
@click.option('--config-file', type=click.Path(exists=True), help='指定配置文件路径')
@click.pass_context
def main(ctx, debug, config_file):
    """悟知 (WuZhi) - 个人知识管理系统
    
    一个基于AI的文档分析和知识管理工具。
    """
    # 确保上下文对象存在
    ctx.ensure_object(dict)
    
    # 设置调试模式
    if debug:
        ctx.obj['debug'] = True
        logger.info("调试模式已启用")
    
    # 加载配置文件
    if config_file:
        ctx.obj['config_file'] = config_file
        logger.info(f"使用配置文件: {config_file}")


# 注册子命令
main.add_command(init_command)
main.add_command(scan_command)
main.add_command(analyze_command)
main.add_command(duplicate_command)
main.add_command(gui_command)
main.add_command(status_command)
main.add_command(export_command)


@main.command()
@click.option('--host', default='localhost', help='服务器主机地址')
@click.option('--port', default=8080, help='服务器端口')
@click.pass_context
def serve(ctx, host, port):
    """启动Web服务器"""
    try:
        from ..ui.app import WuZhiApp
        
        click.echo(f"启动Web服务器: http://{host}:{port}")
        
        app = WuZhiApp()
        app.run(port=port)
        
    except ImportError:
        click.echo("错误: Flet库未安装，无法启动Web服务器", err=True)
        click.echo("请运行: pip install flet", err=True)
    except Exception as e:
        click.echo(f"启动Web服务器失败: {e}", err=True)


@main.command()
@click.argument('query', required=True)
@click.option('--limit', default=10, help='返回结果数量限制')
@click.option('--type', 'doc_type', help='文档类型过滤')
@click.option('--author', help='作者过滤')
@click.pass_context
def search(ctx, query, limit, doc_type, author):
    """搜索文档"""
    try:
        from ..services.document_service import DocumentService
        
        service = DocumentService()
        
        # 构建搜索参数
        filters = {}
        if doc_type:
            filters['document_type'] = doc_type
        if author:
            filters['author'] = author
        
        click.echo(f"搜索: {query}")
        if filters:
            click.echo(f"过滤条件: {filters}")
        
        # 执行搜索
        results = service.search_documents(query, limit=limit, filters=filters)
        
        if not results:
            click.echo("未找到匹配的文档")
            return
        
        click.echo(f"\n找到 {len(results)} 个匹配的文档:")
        click.echo("-" * 60)
        
        for i, doc in enumerate(results, 1):
            click.echo(f"{i}. {doc.get('title', '无标题')}")
            click.echo(f"   文件: {doc.get('file_name', 'N/A')}")
            click.echo(f"   作者: {doc.get('author', 'N/A')}")
            click.echo(f"   类型: {doc.get('document_type', 'N/A')}")
            if doc.get('summary'):
                summary = doc['summary'][:100] + "..." if len(doc['summary']) > 100 else doc['summary']
                click.echo(f"   摘要: {summary}")
            click.echo()
        
    except Exception as e:
        click.echo(f"搜索失败: {e}", err=True)


@main.command()
@click.option('--ai', is_flag=True, help='测试AI功能')
@click.option('--database', is_flag=True, help='测试数据库连接')
@click.option('--all', 'test_all', is_flag=True, help='测试所有组件')
@click.pass_context
def test(ctx, ai, database, test_all):
    """测试系统组件"""
    try:
        if test_all or database:
            click.echo("测试数据库连接...")
            from ..core.database import test_connection
            
            if test_connection():
                click.echo("✓ 数据库连接正常")
            else:
                click.echo("✗ 数据库连接失败", err=True)
        
        if test_all or ai:
            click.echo("测试AI功能...")
            
            async def test_ai_async():
                from ..ai.ai_manager import ai_manager
                
                # 初始化AI管理器
                results = await ai_manager.initialize_all()
                
                if any(results.values()):
                    click.echo("✓ AI功能可用")
                    
                    # 测试AI提供者
                    test_results = await ai_manager.test_all_providers()
                    for provider, result in test_results.items():
                        if result.get('service_available'):
                            click.echo(f"  ✓ {provider}: 服务可用")
                            if result.get('test_generation'):
                                click.echo(f"    ✓ 文本生成测试通过")
                            else:
                                click.echo(f"    ✗ 文本生成测试失败")
                        else:
                            click.echo(f"  ✗ {provider}: 服务不可用")
                else:
                    click.echo("✗ AI功能不可用")
            
            asyncio.run(test_ai_async())
        
        if not (ai or database or test_all):
            click.echo("请指定要测试的组件，或使用 --all 测试所有组件")
            click.echo("可用选项: --ai, --database, --all")
        
    except Exception as e:
        click.echo(f"测试失败: {e}", err=True)


@main.command()
@click.pass_context
def info(ctx):
    """显示系统信息"""
    try:
        click.echo(f"悟知 (WuZhi) v{config.version}")
        click.echo(f"配置目录: {config.config_dir}")
        click.echo(f"数据目录: {config.data_dir}")
        click.echo(f"数据库: {config.database_path}")
        click.echo(f"日志文件: {config.log_file}")
        
        # 显示数据库统计
        from ..core.database import get_db_session
        from ..core.models import Document
        
        with get_db_session() as session:
            total_docs = session.query(Document).count()
            analyzed_docs = session.query(Document).filter(Document.is_analyzed == True).count()
            duplicate_docs = session.query(Document).filter(Document.is_duplicate == True).count()
        
        click.echo("\n数据库统计:")
        click.echo(f"  总文档数: {total_docs}")
        click.echo(f"  已分析: {analyzed_docs}")
        click.echo(f"  重复文档: {duplicate_docs}")
        
        # 显示AI状态
        async def show_ai_status():
            from ..ai.ai_manager import ai_manager
            
            await ai_manager.initialize_all()
            status = ai_manager.get_provider_status()
            
            click.echo("\nAI提供者状态:")
            for name, info in status.items():
                status_text = "可用" if info['available'] else "不可用"
                default_text = " (默认)" if info['is_default'] else ""
                click.echo(f"  {name}: {status_text}{default_text}")
        
        asyncio.run(show_ai_status())
        
    except Exception as e:
        click.echo(f"获取系统信息失败: {e}", err=True)


if __name__ == '__main__':
    main()
