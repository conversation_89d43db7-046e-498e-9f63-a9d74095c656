"""
初始化命令
"""

import click
from pathlib import Path

from ...core.config import config
from ...core.database import init_database, test_connection
from ...core.logger import get_logger

logger = get_logger(__name__)


@click.command('init')
@click.option('--force', is_flag=True, help='强制重新初始化（会清空现有数据）')
@click.option('--data-dir', type=click.Path(), help='指定数据目录')
@click.pass_context
def init_command(ctx, force, data_dir):
    """初始化悟知系统"""
    try:
        click.echo("正在初始化悟知 (WuZhi) 系统...")
        
        # 设置数据目录
        if data_dir:
            data_path = Path(data_dir).resolve()
            click.echo(f"使用指定数据目录: {data_path}")
            # 这里可以更新配置
        else:
            data_path = config.data_dir
            click.echo(f"使用默认数据目录: {data_path}")
        
        # 创建必要的目录
        directories = [
            config.data_dir,
            config.get_cache_dir(),
            config.get_temp_dir(),
            config.database_path.parent,
            config.log_file.parent if config.log_file else None,
        ]
        
        for directory in directories:
            if directory and not directory.exists():
                directory.mkdir(parents=True, exist_ok=True)
                click.echo(f"创建目录: {directory}")
        
        # 检查数据库是否已存在
        if config.database_path.exists() and not force:
            click.echo("数据库已存在。使用 --force 选项强制重新初始化。")
            
            # 测试现有数据库连接
            if test_connection():
                click.echo("✓ 现有数据库连接正常")
                return
            else:
                click.echo("✗ 现有数据库连接失败，将重新初始化")
                force = True
        
        # 初始化数据库
        if force and config.database_path.exists():
            click.echo("删除现有数据库...")
            config.database_path.unlink()
        
        click.echo("初始化数据库...")
        if init_database():
            click.echo("✓ 数据库初始化成功")
        else:
            click.echo("✗ 数据库初始化失败", err=True)
            return
        
        # 测试数据库连接
        if test_connection():
            click.echo("✓ 数据库连接测试通过")
        else:
            click.echo("✗ 数据库连接测试失败", err=True)
            return
        
        # 创建配置文件示例
        env_example = config.config_dir / ".env.example"
        if not env_example.exists():
            try:
                import shutil
                source_env = Path(__file__).parent.parent.parent.parent.parent / ".env.example"
                if source_env.exists():
                    shutil.copy2(source_env, env_example)
                    click.echo(f"创建配置文件示例: {env_example}")
            except Exception as e:
                logger.debug(f"创建配置文件示例失败: {e}")
        
        # 显示下一步操作提示
        click.echo("\n" + "=" * 50)
        click.echo("✓ 悟知系统初始化完成！")
        click.echo("=" * 50)
        click.echo("\n下一步操作:")
        click.echo("1. 扫描文档: wuzhi scan /path/to/documents")
        click.echo("2. 启动图形界面: wuzhi gui")
        click.echo("3. 查看帮助: wuzhi --help")
        
        if env_example.exists():
            click.echo(f"\n配置文件示例: {env_example}")
            click.echo("可以复制为 .env 文件并根据需要修改配置")
        
    except Exception as e:
        click.echo(f"初始化失败: {e}", err=True)
        logger.error(f"初始化失败: {e}")


@click.command('reset')
@click.option('--confirm', is_flag=True, help='确认重置（不提示）')
@click.pass_context
def reset_command(ctx, confirm):
    """重置系统（清空所有数据）"""
    try:
        if not confirm:
            click.echo("警告: 此操作将删除所有数据，包括:")
            click.echo("- 所有文档记录")
            click.echo("- 分析结果")
            click.echo("- 重复检测结果")
            click.echo("- 缓存文件")
            
            if not click.confirm("确定要继续吗？"):
                click.echo("操作已取消")
                return
        
        click.echo("正在重置系统...")
        
        # 删除数据库
        if config.database_path.exists():
            config.database_path.unlink()
            click.echo("✓ 删除数据库")
        
        # 清空缓存目录
        cache_dir = config.get_cache_dir()
        if cache_dir.exists():
            import shutil
            shutil.rmtree(cache_dir)
            click.echo("✓ 清空缓存目录")
        
        # 清空临时目录
        temp_dir = config.get_temp_dir()
        if temp_dir.exists():
            import shutil
            shutil.rmtree(temp_dir)
            click.echo("✓ 清空临时目录")
        
        # 重新初始化
        click.echo("重新初始化系统...")
        ctx.invoke(init_command, force=True)
        
    except Exception as e:
        click.echo(f"重置失败: {e}", err=True)
        logger.error(f"重置失败: {e}")


# 将reset命令添加到init组
init_command.add_command(reset_command)
