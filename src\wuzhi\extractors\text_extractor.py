"""
文本文件内容提取器
"""

from pathlib import Path
from typing import List
import chardet

from .base import BaseExtractor, ExtractionResult
from ..core.models import FileType
from ..core.logger import get_logger

logger = get_logger(__name__)


class TextExtractor(BaseExtractor):
    """文本文件提取器"""
    
    def __init__(self):
        super().__init__()
        self.supported_types = [FileType.TXT]
    
    def can_extract(self, file_path: Path, file_type: FileType) -> bool:
        """检查是否可以提取指定文件"""
        return file_type in self.supported_types and file_path.exists()
    
    def extract(self, file_path: Path) -> ExtractionResult:
        """提取文本文件内容"""
        try:
            # 检测文件编码
            encoding = self._detect_encoding(file_path)
            if not encoding:
                return self._create_error_result("无法检测文件编码")
            
            # 读取文件内容
            with open(file_path, 'r', encoding=encoding) as f:
                content = f.read()
            
            if not content.strip():
                return self._create_error_result("文件内容为空")
            
            # 清理文本
            cleaned_content = self._clean_text(content)
            
            # 提取结构化信息
            paragraphs = self._extract_paragraphs(content)
            headings = self._extract_headings(content)
            
            # 从文本中提取元数据
            metadata = self._extract_metadata_from_text(content)
            
            return self._create_success_result(
                text_content=content,
                plain_text=cleaned_content,
                paragraphs=paragraphs,
                headings=headings,
                title=metadata.get('potential_title'),
                metadata={
                    'encoding': encoding,
                    'line_count': len(content.split('\n')),
                    **metadata
                }
            )
            
        except UnicodeDecodeError as e:
            logger.error(f"文本文件编码错误 {file_path}: {e}")
            return self._create_error_result(f"文件编码错误: {e}")
        except Exception as e:
            logger.error(f"文本文件提取失败 {file_path}: {e}")
            return self._create_error_result(f"提取失败: {e}")
    
    def _detect_encoding(self, file_path: Path) -> str:
        """检测文件编码"""
        try:
            # 读取文件的一部分来检测编码
            with open(file_path, 'rb') as f:
                raw_data = f.read(10240)  # 读取前10KB
            
            # 使用chardet检测编码
            result = chardet.detect(raw_data)
            if result and result['confidence'] > 0.7:
                encoding = result['encoding']
                logger.debug(f"检测到编码 {file_path}: {encoding} (置信度: {result['confidence']})")
                return encoding
            
            # 尝试常见编码
            common_encodings = ['utf-8', 'gbk', 'gb2312', 'utf-16', 'latin1']
            for encoding in common_encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        f.read(1024)  # 尝试读取一部分
                    logger.debug(f"使用编码 {file_path}: {encoding}")
                    return encoding
                except UnicodeDecodeError:
                    continue
            
            logger.warning(f"无法确定文件编码 {file_path}，使用utf-8")
            return 'utf-8'
            
        except Exception as e:
            logger.error(f"编码检测失败 {file_path}: {e}")
            return 'utf-8'
    
    def _extract_paragraphs(self, content: str) -> List[str]:
        """提取段落"""
        if not content:
            return []
        
        # 按空行分割段落
        paragraphs = []
        current_paragraph = []
        
        for line in content.split('\n'):
            line = line.strip()
            if line:
                current_paragraph.append(line)
            else:
                if current_paragraph:
                    paragraphs.append(' '.join(current_paragraph))
                    current_paragraph = []
        
        # 添加最后一个段落
        if current_paragraph:
            paragraphs.append(' '.join(current_paragraph))
        
        # 过滤太短的段落
        return [p for p in paragraphs if len(p.strip()) > 10]
    
    def _extract_headings(self, content: str) -> List[str]:
        """提取标题（简单实现）"""
        headings = []
        
        for line in content.split('\n'):
            line = line.strip()
            if not line:
                continue
            
            # 简单的标题检测规则
            # 1. 行长度适中（不太长不太短）
            # 2. 不以标点符号结尾
            # 3. 可能包含数字编号
            if (10 <= len(line) <= 100 and 
                not line.endswith(('.', '。', '!', '！', '?', '？')) and
                (line[0].isdigit() or line[0].isupper() or '\u4e00' <= line[0] <= '\u9fff')):
                headings.append(line)
        
        return headings[:20]  # 最多返回20个标题
    
    def _extract_metadata_from_text(self, text: str) -> dict:
        """从文本内容中提取元数据"""
        metadata = super()._extract_metadata_from_text(text)
        
        lines = text.split('\n')
        
        # 查找可能的作者信息
        author_keywords = ['作者', 'author', '著', '编著', 'by']
        for line in lines[:10]:  # 只检查前10行
            line_lower = line.lower().strip()
            for keyword in author_keywords:
                if keyword in line_lower:
                    # 提取作者名
                    parts = line.split(keyword)
                    if len(parts) > 1:
                        potential_author = parts[1].strip(' :：')
                        if potential_author and len(potential_author) < 50:
                            metadata['potential_author'] = potential_author
                            break
        
        # 查找日期信息
        import re
        date_pattern = r'\d{4}[-/年]\d{1,2}[-/月]\d{1,2}[日]?'
        for line in lines[:20]:
            matches = re.findall(date_pattern, line)
            if matches:
                metadata['potential_dates'] = matches
                break
        
        return metadata
