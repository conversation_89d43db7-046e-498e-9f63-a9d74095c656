"""
图形界面启动命令
"""

import click
import sys

from ...core.logger import get_logger

logger = get_logger(__name__)


@click.command('gui')
@click.option('--port', default=8080, help='Web服务器端口')
@click.option('--host', default='localhost', help='Web服务器主机地址')
@click.option('--debug', is_flag=True, help='启用调试模式')
@click.pass_context
def gui_command(ctx, port, host, debug):
    """启动图形用户界面"""
    try:
        click.echo("正在启动悟知图形界面...")
        
        # 检查Flet是否可用
        try:
            import flet as ft
        except ImportError:
            click.echo("错误: Flet库未安装", err=True)
            click.echo("请运行以下命令安装:")
            click.echo("  pip install flet")
            return
        
        # 启动应用
        from ...ui.app import WuZhiApp
        
        click.echo(f"启动Web服务器: http://{host}:{port}")
        click.echo("按 Ctrl+C 停止服务器")
        
        app = WuZhiApp()
        
        # 设置调试模式
        if debug or ctx.obj.get('debug'):
            app.debug = True
        
        # 启动应用
        app.run(port=port, host=host)
        
    except KeyboardInterrupt:
        click.echo("\n图形界面已停止")
    except ImportError as e:
        click.echo(f"导入错误: {e}", err=True)
        click.echo("请确保已安装所有必需的依赖")
    except Exception as e:
        click.echo(f"启动图形界面失败: {e}", err=True)
        logger.error(f"启动图形界面失败: {e}")


@click.command('desktop')
@click.pass_context
def desktop_command(ctx):
    """启动桌面应用模式"""
    try:
        click.echo("正在启动悟知桌面应用...")
        
        # 检查Flet是否可用
        try:
            import flet as ft
        except ImportError:
            click.echo("错误: Flet库未安装", err=True)
            click.echo("请运行以下命令安装:")
            click.echo("  pip install flet")
            return
        
        # 启动桌面应用
        from ...ui.app import WuZhiApp
        
        app = WuZhiApp()
        
        # 设置调试模式
        if ctx.obj.get('debug'):
            app.debug = True
        
        # 启动桌面模式
        app.run_desktop()
        
    except KeyboardInterrupt:
        click.echo("\n桌面应用已停止")
    except ImportError as e:
        click.echo(f"导入错误: {e}", err=True)
        click.echo("请确保已安装所有必需的依赖")
    except Exception as e:
        click.echo(f"启动桌面应用失败: {e}", err=True)
        logger.error(f"启动桌面应用失败: {e}")


# 将desktop命令添加到gui组
gui_command.add_command(desktop_command)
