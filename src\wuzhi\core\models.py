"""
数据模型定义
"""

from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field
from sqlalchemy import Column, Integer, String, Text, DateTime, Float, Boolean, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func

Base = declarative_base()


class DocumentType(str, Enum):
    """文档类型枚举"""
    BOOK = "book"           # 书籍
    PAPER = "paper"         # 论文
    REPORT = "report"       # 报告
    SUMMARY = "summary"     # 总结
    ARTICLE = "article"     # 文章
    MANUAL = "manual"       # 手册
    PRESENTATION = "presentation"  # 演示文稿
    OTHER = "other"         # 其他


class FileType(str, Enum):
    """文件类型枚举"""
    TXT = "txt"
    PDF = "pdf"
    EPUB = "epub"
    DOC = "doc"
    DOCX = "docx"
    WPS = "wps"
    CEB = "ceb"
    PPT = "ppt"
    PPTX = "pptx"
    MD = "md"
    UNKNOWN = "unknown"


class Language(str, Enum):
    """语言枚举"""
    CHINESE = "zh"
    ENGLISH = "en"
    JAPANESE = "ja"
    KOREAN = "ko"
    FRENCH = "fr"
    GERMAN = "de"
    SPANISH = "es"
    RUSSIAN = "ru"
    UNKNOWN = "unknown"


class Document(Base):
    """文档数据模型"""
    __tablename__ = "documents"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # 基本信息
    file_path = Column(String(500), nullable=False, index=True)
    file_name = Column(String(255), nullable=False)
    file_type = Column(String(20), nullable=False)
    file_size = Column(Integer, nullable=False)
    file_hash = Column(String(64), nullable=False, index=True)
    
    # 文档信息
    document_type = Column(String(50), nullable=True)
    title = Column(String(500), nullable=True)
    author = Column(String(200), nullable=True)
    publisher = Column(String(200), nullable=True)
    publish_date = Column(String(50), nullable=True)
    language = Column(String(10), nullable=True)
    
    # 内容统计
    page_count = Column(Integer, nullable=True)
    word_count = Column(Integer, nullable=True)
    char_count = Column(Integer, nullable=True)
    
    # 分析结果
    keywords = Column(JSON, nullable=True)  # {"keyword": count, ...}
    summary = Column(Text, nullable=True)
    summary_zh = Column(Text, nullable=True)
    
    # 元数据
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    analyzed_at = Column(DateTime, nullable=True)
    is_analyzed = Column(Boolean, default=False)
    is_duplicate = Column(Boolean, default=False)
    
    # 额外信息
    metadata = Column(JSON, nullable=True)


class Keyword(Base):
    """关键词统计模型"""
    __tablename__ = "keywords"
    
    id = Column(Integer, primary_key=True, index=True)
    keyword = Column(String(100), nullable=False, unique=True, index=True)
    document_count = Column(Integer, default=0)  # 出现在多少个文档中
    total_frequency = Column(Integer, default=0)  # 总出现次数
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())


class DocumentKeyword(Base):
    """文档-关键词关联模型"""
    __tablename__ = "document_keywords"
    
    id = Column(Integer, primary_key=True, index=True)
    document_id = Column(Integer, nullable=False, index=True)
    keyword_id = Column(Integer, nullable=False, index=True)
    frequency = Column(Integer, default=1)  # 在该文档中的出现次数


class DuplicateGroup(Base):
    """重复文档组模型"""
    __tablename__ = "duplicate_groups"
    
    id = Column(Integer, primary_key=True, index=True)
    group_hash = Column(String(64), nullable=False, index=True)
    similarity_score = Column(Float, nullable=False)
    document_ids = Column(JSON, nullable=False)  # [doc_id1, doc_id2, ...]
    created_at = Column(DateTime, default=func.now())


# Pydantic模型用于API和数据传输
class DocumentBase(BaseModel):
    """文档基础模型"""
    file_path: str
    file_name: str
    file_type: FileType
    file_size: int
    file_hash: str


class DocumentCreate(DocumentBase):
    """创建文档模型"""
    pass


class DocumentUpdate(BaseModel):
    """更新文档模型"""
    document_type: Optional[DocumentType] = None
    title: Optional[str] = None
    author: Optional[str] = None
    publisher: Optional[str] = None
    publish_date: Optional[str] = None
    language: Optional[Language] = None
    page_count: Optional[int] = None
    word_count: Optional[int] = None
    char_count: Optional[int] = None
    keywords: Optional[Dict[str, int]] = None
    summary: Optional[str] = None
    summary_zh: Optional[str] = None
    is_analyzed: bool = False
    analyzed_at: Optional[datetime] = None
    metadata: Optional[Dict[str, Any]] = None


class DocumentResponse(DocumentBase):
    """文档响应模型"""
    id: int
    document_type: Optional[DocumentType] = None
    title: Optional[str] = None
    author: Optional[str] = None
    publisher: Optional[str] = None
    publish_date: Optional[str] = None
    language: Optional[Language] = None
    page_count: Optional[int] = None
    word_count: Optional[int] = None
    char_count: Optional[int] = None
    keywords: Optional[Dict[str, int]] = None
    summary: Optional[str] = None
    summary_zh: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    analyzed_at: Optional[datetime] = None
    is_analyzed: bool
    is_duplicate: bool
    
    class Config:
        from_attributes = True


class AnalysisResult(BaseModel):
    """分析结果模型"""
    document_type: Optional[DocumentType] = None
    title: Optional[str] = None
    author: Optional[str] = None
    publisher: Optional[str] = None
    publish_date: Optional[str] = None
    language: Optional[Language] = None
    page_count: Optional[int] = None
    word_count: Optional[int] = None
    char_count: Optional[int] = None
    keywords: Dict[str, int] = Field(default_factory=dict)
    summary: Optional[str] = None
    summary_zh: Optional[str] = None
    confidence: float = 0.0  # 分析结果置信度
    metadata: Dict[str, Any] = Field(default_factory=dict)


class KeywordStats(BaseModel):
    """关键词统计模型"""
    keyword: str
    document_count: int
    total_frequency: int
    documents: List[DocumentResponse] = Field(default_factory=list)


class DuplicateGroupResponse(BaseModel):
    """重复文档组响应模型"""
    id: int
    group_hash: str
    similarity_score: float
    documents: List[DocumentResponse]
    created_at: datetime
    
    class Config:
        from_attributes = True
