"""
EPUB文件内容提取器
"""

from pathlib import Path
from typing import List, Dict, Any
import zipfile
import xml.etree.ElementTree as ET
from html import unescape
import re

from .base import BaseExtractor, ExtractionResult
from ..core.models import FileType
from ..core.logger import get_logger

logger = get_logger(__name__)


class EPUBExtractor(BaseExtractor):
    """EPUB文件提取器"""
    
    def __init__(self):
        super().__init__()
        self.supported_types = [FileType.EPUB]
    
    def can_extract(self, file_path: Path, file_type: FileType) -> bool:
        """检查是否可以提取指定文件"""
        return file_type in self.supported_types and file_path.exists()
    
    def extract(self, file_path: Path) -> ExtractionResult:
        """提取EPUB文件内容"""
        try:
            with zipfile.ZipFile(file_path, 'r') as epub_zip:
                # 验证EPUB格式
                if not self._is_valid_epub(epub_zip):
                    return self._create_error_result("不是有效的EPUB文件")
                
                # 提取元数据
                metadata = self._extract_epub_metadata(epub_zip)
                
                # 提取文本内容
                text_content, chapters = self._extract_text_content(epub_zip)
                
                if not text_content.strip():
                    return self._create_error_result("无法提取文本内容")
                
                # 清理文本
                cleaned_text = self._clean_text(text_content)
                
                # 提取结构化信息
                paragraphs = self._extract_paragraphs(cleaned_text)
                headings = self._extract_headings_from_chapters(chapters)
                
                return self._create_success_result(
                    text_content=text_content,
                    plain_text=cleaned_text,
                    title=metadata.get('title'),
                    author=metadata.get('author'),
                    subject=metadata.get('subject'),
                    creator=metadata.get('creator'),
                    creation_date=metadata.get('creation_date'),
                    paragraphs=paragraphs,
                    headings=headings,
                    metadata={
                        'extractor': 'epub',
                        'chapter_count': len(chapters),
                        'language': metadata.get('language'),
                        'publisher': metadata.get('publisher'),
                        'identifier': metadata.get('identifier'),
                        **metadata
                    }
                )
                
        except zipfile.BadZipFile:
            return self._create_error_result("文件不是有效的ZIP格式")
        except Exception as e:
            logger.error(f"EPUB文件提取失败 {file_path}: {e}")
            return self._create_error_result(f"提取失败: {e}")
    
    def _is_valid_epub(self, epub_zip: zipfile.ZipFile) -> bool:
        """验证是否为有效的EPUB文件"""
        try:
            # 检查必需的文件
            file_list = epub_zip.namelist()
            
            # 必须包含mimetype文件
            if 'mimetype' not in file_list:
                return False
            
            # 检查mimetype内容
            mimetype_content = epub_zip.read('mimetype').decode('utf-8').strip()
            if mimetype_content != 'application/epub+zip':
                return False
            
            # 必须包含META-INF/container.xml
            if 'META-INF/container.xml' not in file_list:
                return False
            
            return True
            
        except Exception as e:
            logger.debug(f"EPUB验证失败: {e}")
            return False
    
    def _extract_epub_metadata(self, epub_zip: zipfile.ZipFile) -> Dict[str, Any]:
        """提取EPUB元数据"""
        metadata = {}
        
        try:
            # 读取container.xml获取OPF文件路径
            container_xml = epub_zip.read('META-INF/container.xml')
            container_root = ET.fromstring(container_xml)
            
            # 查找OPF文件
            opf_path = None
            for rootfile in container_root.findall('.//{urn:oasis:names:tc:opendocument:xmlns:container}rootfile'):
                if rootfile.get('media-type') == 'application/oebps-package+xml':
                    opf_path = rootfile.get('full-path')
                    break
            
            if not opf_path:
                logger.warning("未找到OPF文件")
                return metadata
            
            # 读取OPF文件
            opf_content = epub_zip.read(opf_path)
            opf_root = ET.fromstring(opf_content)
            
            # 提取元数据
            metadata_elem = opf_root.find('.//{http://www.idpf.org/2007/opf}metadata')
            if metadata_elem is not None:
                # Dublin Core元数据
                dc_ns = '{http://purl.org/dc/elements/1.1/}'
                
                # 标题
                title_elem = metadata_elem.find(f'{dc_ns}title')
                if title_elem is not None:
                    metadata['title'] = title_elem.text
                
                # 作者
                creator_elems = metadata_elem.findall(f'{dc_ns}creator')
                authors = []
                for creator in creator_elems:
                    if creator.text:
                        authors.append(creator.text)
                if authors:
                    metadata['author'] = ', '.join(authors)
                    metadata['creator'] = authors[0]  # 主要创作者
                
                # 其他元数据
                for field in ['subject', 'description', 'publisher', 'date', 'language', 'identifier']:
                    elem = metadata_elem.find(f'{dc_ns}{field}')
                    if elem is not None and elem.text:
                        metadata[field] = elem.text
                
                # 处理日期
                if 'date' in metadata:
                    metadata['creation_date'] = self._parse_date(metadata['date'])
            
            # 提取目录信息
            manifest_elem = opf_root.find('.//{http://www.idpf.org/2007/opf}manifest')
            if manifest_elem is not None:
                items = manifest_elem.findall('.//{http://www.idpf.org/2007/opf}item')
                metadata['item_count'] = len(items)
                
                # 统计不同类型的文件
                media_types = {}
                for item in items:
                    media_type = item.get('media-type', 'unknown')
                    media_types[media_type] = media_types.get(media_type, 0) + 1
                metadata['media_types'] = media_types
            
        except Exception as e:
            logger.warning(f"提取EPUB元数据失败: {e}")
        
        return metadata
    
    def _extract_text_content(self, epub_zip: zipfile.ZipFile) -> tuple:
        """提取EPUB文本内容"""
        text_content = ""
        chapters = []
        
        try:
            # 获取OPF文件路径
            container_xml = epub_zip.read('META-INF/container.xml')
            container_root = ET.fromstring(container_xml)
            
            opf_path = None
            for rootfile in container_root.findall('.//{urn:oasis:names:tc:opendocument:xmlns:container}rootfile'):
                if rootfile.get('media-type') == 'application/oebps-package+xml':
                    opf_path = rootfile.get('full-path')
                    break
            
            if not opf_path:
                return text_content, chapters
            
            # 读取OPF文件
            opf_content = epub_zip.read(opf_path)
            opf_root = ET.fromstring(opf_content)
            
            # 获取基础路径
            base_path = str(Path(opf_path).parent)
            if base_path == '.':
                base_path = ''
            
            # 获取spine中的阅读顺序
            spine_elem = opf_root.find('.//{http://www.idpf.org/2007/opf}spine')
            manifest_elem = opf_root.find('.//{http://www.idpf.org/2007/opf}manifest')
            
            if spine_elem is None or manifest_elem is None:
                return text_content, chapters
            
            # 创建ID到href的映射
            id_to_href = {}
            for item in manifest_elem.findall('.//{http://www.idpf.org/2007/opf}item'):
                item_id = item.get('id')
                href = item.get('href')
                if item_id and href:
                    id_to_href[item_id] = href
            
            # 按spine顺序提取内容
            for itemref in spine_elem.findall('.//{http://www.idpf.org/2007/opf}itemref'):
                idref = itemref.get('idref')
                if idref in id_to_href:
                    href = id_to_href[idref]
                    file_path = f"{base_path}/{href}" if base_path else href
                    
                    try:
                        # 读取HTML/XHTML文件
                        html_content = epub_zip.read(file_path).decode('utf-8')
                        
                        # 提取文本
                        chapter_text = self._extract_text_from_html(html_content)
                        if chapter_text.strip():
                            chapters.append({
                                'file': file_path,
                                'text': chapter_text
                            })
                            text_content += chapter_text + "\n\n"
                            
                    except Exception as e:
                        logger.debug(f"提取章节失败 {file_path}: {e}")
                        continue
        
        except Exception as e:
            logger.error(f"提取EPUB内容失败: {e}")
        
        return text_content, chapters
    
    def _extract_text_from_html(self, html_content: str) -> str:
        """从HTML中提取纯文本"""
        try:
            # 移除脚本和样式标签
            html_content = re.sub(r'<script.*?</script>', '', html_content, flags=re.DOTALL | re.IGNORECASE)
            html_content = re.sub(r'<style.*?</style>', '', html_content, flags=re.DOTALL | re.IGNORECASE)
            
            # 移除HTML标签
            text = re.sub(r'<[^>]+>', ' ', html_content)
            
            # 解码HTML实体
            text = unescape(text)
            
            # 清理空白字符
            text = re.sub(r'\s+', ' ', text)
            text = text.strip()
            
            return text
            
        except Exception as e:
            logger.debug(f"HTML文本提取失败: {e}")
            return ""
    
    def _extract_paragraphs(self, text: str) -> List[str]:
        """提取段落"""
        if not text:
            return []
        
        # 按双换行符分割段落
        paragraphs = []
        for para in text.split('\n\n'):
            para = para.strip()
            if para and len(para) > 20:  # 过滤太短的段落
                paragraphs.append(para)
        
        return paragraphs
    
    def _extract_headings_from_chapters(self, chapters: List[Dict]) -> List[str]:
        """从章节中提取标题"""
        headings = []
        
        for chapter in chapters:
            text = chapter['text']
            lines = text.split('\n')
            
            # 查找可能的标题（通常在章节开头）
            for line in lines[:5]:  # 只检查前5行
                line = line.strip()
                if (line and 
                    len(line) < 100 and  # 标题不会太长
                    not line.endswith('.') and  # 标题通常不以句号结尾
                    len(line.split()) <= 10):  # 标题词数不会太多
                    headings.append(line)
                    break
        
        return headings
