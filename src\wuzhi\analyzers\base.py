"""
基础文档分析器
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from pathlib import Path
from typing import Dict, List, Optional, Any
from datetime import datetime

from ..core.models import DocumentType, Language, FileType
from ..extractors.base import ExtractionResult


@dataclass
class AnalysisResult:
    """文档分析结果"""
    
    # 基本信息
    success: bool = False
    error_message: Optional[str] = None
    confidence: float = 0.0  # 分析结果置信度 (0-1)
    
    # 文档分类
    document_type: Optional[DocumentType] = None
    document_type_confidence: float = 0.0
    
    # 元数据
    title: Optional[str] = None
    title_confidence: float = 0.0
    author: Optional[str] = None
    author_confidence: float = 0.0
    publisher: Optional[str] = None
    publish_date: Optional[str] = None
    language: Optional[Language] = None
    
    # 内容分析
    keywords: Dict[str, int] = field(default_factory=dict)
    summary: Optional[str] = None
    summary_zh: Optional[str] = None
    
    # 统计信息
    page_count: Optional[int] = None
    word_count: Optional[int] = None
    char_count: Optional[int] = None
    
    # 结构化信息
    headings: List[str] = field(default_factory=list)
    main_topics: List[str] = field(default_factory=list)
    
    # 质量评估
    text_quality: float = 0.0  # 文本质量评分 (0-1)
    completeness: float = 0.0  # 信息完整性评分 (0-1)
    
    # 额外信息
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def get_overall_confidence(self) -> float:
        """获取整体置信度"""
        confidences = [
            self.confidence,
            self.document_type_confidence,
            self.title_confidence,
            self.author_confidence,
        ]
        
        # 过滤掉0值
        valid_confidences = [c for c in confidences if c > 0]
        
        if not valid_confidences:
            return 0.0
        
        return sum(valid_confidences) / len(valid_confidences)
    
    def is_high_quality(self) -> bool:
        """判断是否为高质量分析结果"""
        return (self.success and 
                self.get_overall_confidence() > 0.7 and
                self.text_quality > 0.6 and
                self.completeness > 0.5)
    
    def get_summary_dict(self) -> Dict[str, Any]:
        """获取摘要字典"""
        return {
            'success': self.success,
            'confidence': self.get_overall_confidence(),
            'document_type': self.document_type.value if self.document_type else None,
            'title': self.title,
            'author': self.author,
            'language': self.language.value if self.language else None,
            'word_count': self.word_count,
            'keyword_count': len(self.keywords),
            'has_summary': bool(self.summary),
            'text_quality': self.text_quality,
            'completeness': self.completeness,
        }


class BaseAnalyzer(ABC):
    """基础文档分析器抽象类"""
    
    def __init__(self):
        self.name = self.__class__.__name__
        self.version = "1.0.0"
    
    @abstractmethod
    def analyze(self, extraction_result: ExtractionResult, file_path: Path = None) -> AnalysisResult:
        """分析文档内容"""
        pass
    
    def can_analyze(self, extraction_result: ExtractionResult) -> bool:
        """检查是否可以分析指定内容"""
        return extraction_result.success and bool(extraction_result.plain_text.strip())
    
    def _create_error_result(self, error_message: str) -> AnalysisResult:
        """创建错误结果"""
        return AnalysisResult(
            success=False,
            error_message=error_message
        )
    
    def _create_success_result(self, **kwargs) -> AnalysisResult:
        """创建成功结果"""
        return AnalysisResult(success=True, **kwargs)
    
    def _calculate_text_quality(self, text: str) -> float:
        """计算文本质量评分"""
        if not text:
            return 0.0
        
        quality_score = 0.0
        
        # 长度评分 (0.3权重)
        length_score = min(len(text) / 1000, 1.0)  # 1000字符为满分
        quality_score += length_score * 0.3
        
        # 字符多样性评分 (0.2权重)
        unique_chars = len(set(text.lower()))
        diversity_score = min(unique_chars / 50, 1.0)  # 50个不同字符为满分
        quality_score += diversity_score * 0.2
        
        # 句子结构评分 (0.3权重)
        sentences = text.count('.') + text.count('。') + text.count('!') + text.count('？')
        if len(text) > 0:
            sentence_density = sentences / (len(text) / 100)  # 每100字符的句子数
            structure_score = min(sentence_density / 3, 1.0)  # 3句/100字符为满分
            quality_score += structure_score * 0.3
        
        # 可读性评分 (0.2权重)
        # 简单的可读性评估：字母数字比例
        alphanumeric = sum(1 for c in text if c.isalnum())
        if len(text) > 0:
            readability_score = alphanumeric / len(text)
            quality_score += readability_score * 0.2
        
        return min(quality_score, 1.0)
    
    def _calculate_completeness(self, analysis_result: AnalysisResult) -> float:
        """计算信息完整性评分"""
        completeness_score = 0.0
        
        # 基本信息完整性
        if analysis_result.title:
            completeness_score += 0.25
        if analysis_result.author:
            completeness_score += 0.20
        if analysis_result.document_type:
            completeness_score += 0.15
        if analysis_result.language:
            completeness_score += 0.10
        
        # 内容分析完整性
        if analysis_result.keywords:
            completeness_score += 0.15
        if analysis_result.summary:
            completeness_score += 0.10
        
        # 统计信息完整性
        if analysis_result.word_count and analysis_result.word_count > 0:
            completeness_score += 0.05
        
        return min(completeness_score, 1.0)
    
    def _extract_potential_title(self, text: str, headings: List[str] = None) -> tuple:
        """提取潜在标题"""
        if not text:
            return None, 0.0
        
        # 优先使用提取的标题
        if headings:
            for heading in headings[:3]:  # 只考虑前3个标题
                if heading and len(heading.strip()) > 5:
                    title = heading.strip().lstrip('#').strip()
                    if len(title) < 200:  # 标题不应太长
                        return title, 0.9
        
        # 从文本开头提取标题
        lines = text.split('\n')[:10]  # 只检查前10行
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # 标题特征检测
            if (5 <= len(line) <= 200 and  # 长度适中
                not line.endswith('.') and  # 不以句号结尾
                not line.startswith(('http', 'www', 'ftp')) and  # 不是链接
                line[0].isupper() or '\u4e00' <= line[0] <= '\u9fff'):  # 首字母大写或中文
                
                # 计算置信度
                confidence = 0.5
                
                # 如果在文档开头，置信度更高
                if lines.index(line) <= 2:
                    confidence += 0.2
                
                # 如果包含常见标题词汇，置信度更高
                title_keywords = ['研究', '分析', '报告', '论文', '手册', '指南', 'study', 'analysis', 'report']
                if any(keyword in line.lower() for keyword in title_keywords):
                    confidence += 0.1
                
                return line, min(confidence, 1.0)
        
        return None, 0.0
    
    def _extract_potential_author(self, text: str, metadata: Dict = None) -> tuple:
        """提取潜在作者"""
        if metadata and 'author' in metadata:
            return metadata['author'], 0.9
        
        if not text:
            return None, 0.0
        
        import re
        
        # 作者模式匹配
        author_patterns = [
            r'作者[：:]\s*([^\n\r]{2,30})',
            r'著[：:]\s*([^\n\r]{2,30})',
            r'编著[：:]\s*([^\n\r]{2,30})',
            r'Author[：:]\s*([^\n\r]{2,50})',
            r'By[：:]\s*([^\n\r]{2,50})',
            r'Written by[：:]\s*([^\n\r]{2,50})',
        ]
        
        lines = text.split('\n')[:20]  # 只检查前20行
        text_to_search = '\n'.join(lines)
        
        for pattern in author_patterns:
            matches = re.findall(pattern, text_to_search, re.IGNORECASE)
            if matches:
                author = matches[0].strip()
                if author and len(author) < 50:
                    return author, 0.7
        
        return None, 0.0
    
    def __str__(self) -> str:
        return f"{self.name} v{self.version}"
