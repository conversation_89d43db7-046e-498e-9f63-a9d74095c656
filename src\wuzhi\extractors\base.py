"""
基础内容提取器
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from pathlib import Path
from typing import Dict, List, Optional, Any
from datetime import datetime

from ..core.models import FileType, Language


@dataclass
class ExtractionResult:
    """内容提取结果"""
    
    # 基本信息
    success: bool = False
    error_message: Optional[str] = None
    
    # 文本内容
    text_content: str = ""
    plain_text: str = ""  # 纯文本，去除格式
    
    # 元数据
    title: Optional[str] = None
    author: Optional[str] = None
    subject: Optional[str] = None
    creator: Optional[str] = None
    producer: Optional[str] = None
    creation_date: Optional[datetime] = None
    modification_date: Optional[datetime] = None
    
    # 文档属性
    page_count: Optional[int] = None
    word_count: Optional[int] = None
    char_count: Optional[int] = None
    language: Optional[Language] = None
    
    # 结构化信息
    headings: List[str] = None
    paragraphs: List[str] = None
    tables: List[Dict] = None
    images: List[Dict] = None
    links: List[Dict] = None
    
    # 额外元数据
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        """初始化后处理"""
        if self.headings is None:
            self.headings = []
        if self.paragraphs is None:
            self.paragraphs = []
        if self.tables is None:
            self.tables = []
        if self.images is None:
            self.images = []
        if self.links is None:
            self.links = []
        if self.metadata is None:
            self.metadata = {}
    
    def get_full_text(self) -> str:
        """获取完整文本内容"""
        if self.plain_text:
            return self.plain_text
        return self.text_content
    
    def get_summary_info(self) -> Dict[str, Any]:
        """获取摘要信息"""
        return {
            'title': self.title,
            'author': self.author,
            'page_count': self.page_count,
            'word_count': self.word_count,
            'char_count': self.char_count,
            'language': self.language.value if self.language else None,
            'creation_date': self.creation_date.isoformat() if self.creation_date else None,
            'has_images': len(self.images) > 0,
            'has_tables': len(self.tables) > 0,
            'has_links': len(self.links) > 0,
        }


class BaseExtractor(ABC):
    """基础内容提取器抽象类"""
    
    def __init__(self):
        self.supported_types: List[FileType] = []
        self.name = self.__class__.__name__
    
    @abstractmethod
    def can_extract(self, file_path: Path, file_type: FileType) -> bool:
        """检查是否可以提取指定文件"""
        pass
    
    @abstractmethod
    def extract(self, file_path: Path) -> ExtractionResult:
        """提取文档内容"""
        pass
    
    def _create_error_result(self, error_message: str) -> ExtractionResult:
        """创建错误结果"""
        return ExtractionResult(
            success=False,
            error_message=error_message
        )
    
    def _create_success_result(self, **kwargs) -> ExtractionResult:
        """创建成功结果"""
        result = ExtractionResult(success=True, **kwargs)
        
        # 自动计算字数和字符数
        if result.plain_text and not result.word_count:
            from ..utils.text_utils import count_words, detect_language
            
            # 检测语言
            if not result.language:
                result.language = detect_language(result.plain_text)
            
            # 计算字数
            word_count, char_count = count_words(result.plain_text, result.language)
            result.word_count = word_count
            result.char_count = char_count
        
        return result
    
    def _clean_text(self, text: str) -> str:
        """清理文本内容"""
        if not text:
            return ""
        
        from ..utils.text_utils import clean_text
        return clean_text(text)
    
    def _extract_metadata_from_text(self, text: str) -> Dict[str, Any]:
        """从文本中提取元数据（基础实现）"""
        metadata = {}
        
        if not text:
            return metadata
        
        lines = text.split('\n')[:20]  # 只检查前20行
        
        # 尝试提取标题（通常在文档开头）
        for line in lines:
            line = line.strip()
            if line and len(line) < 200:  # 标题通常不会太长
                # 简单的标题检测逻辑
                if not any(char.isdigit() for char in line[:10]):  # 开头不包含数字
                    metadata['potential_title'] = line
                    break
        
        return metadata
    
    def _parse_date(self, date_str: str) -> Optional[datetime]:
        """解析日期字符串"""
        if not date_str:
            return None
        
        # 常见日期格式
        date_formats = [
            '%Y-%m-%d',
            '%Y/%m/%d',
            '%d/%m/%Y',
            '%m/%d/%Y',
            '%Y-%m-%d %H:%M:%S',
            '%Y/%m/%d %H:%M:%S',
        ]
        
        for fmt in date_formats:
            try:
                return datetime.strptime(date_str, fmt)
            except ValueError:
                continue
        
        return None
    
    def get_supported_types(self) -> List[FileType]:
        """获取支持的文件类型"""
        return self.supported_types.copy()
    
    def __str__(self) -> str:
        return f"{self.name}({', '.join([t.value for t in self.supported_types])})"
