"""
配置管理模块
"""

import os
from pathlib import Path
from typing import Dict, Any, Optional
from pydantic import BaseSettings, Field


class Config(BaseSettings):
    """应用配置类"""
    
    # 基础配置
    app_name: str = "悟知 (<PERSON><PERSON><PERSON>)"
    version: str = "0.1.0"
    debug: bool = False
    
    # 数据库配置
    database_url: str = Field(
        default="sqlite:///./data/wuzhi.db",
        description="数据库连接URL"
    )
    
    # 文件处理配置
    max_file_size: int = Field(
        default=100 * 1024 * 1024,  # 100MB
        description="最大文件大小（字节）"
    )
    
    supported_extensions: list = Field(
        default=[
            ".txt", ".pdf", ".epub", ".doc", ".docx", 
            ".wps", ".ceb", ".ppt", ".pptx", ".md"
        ],
        description="支持的文件扩展名"
    )
    
    # AI配置
    use_ai_summary: bool = Field(
        default=True,
        description="是否使用AI生成摘要"
    )
    
    ollama_base_url: str = Field(
        default="http://localhost:11434",
        description="Ollama服务地址"
    )
    
    ollama_model: str = Field(
        default="qwen:4b",
        description="使用的Ollama模型"
    )
    
    # OCR配置
    use_ocr: bool = Field(
        default=True,
        description="是否启用OCR功能"
    )
    
    tesseract_cmd: Optional[str] = Field(
        default=None,
        description="Tesseract命令路径"
    )
    
    # 分析配置
    max_keywords: int = Field(
        default=20,
        description="最大关键词数量"
    )
    
    min_summary_ratio: float = Field(
        default=0.01,
        description="摘要最小比例（相对于文档字数）"
    )
    
    # 重复检测配置
    similarity_threshold: float = Field(
        default=0.85,
        description="重复文档相似度阈值"
    )
    
    # 日志配置
    log_level: str = Field(
        default="INFO",
        description="日志级别"
    )
    
    log_file: str = Field(
        default="./data/logs/wuzhi.log",
        description="日志文件路径"
    )
    
    # UI配置
    window_width: int = Field(
        default=1200,
        description="窗口宽度"
    )
    
    window_height: int = Field(
        default=800,
        description="窗口高度"
    )
    
    theme_mode: str = Field(
        default="system",
        description="主题模式: light, dark, system"
    )
    
    class Config:
        env_file = ".env"
        env_prefix = "WUZHI_"
        case_sensitive = False
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self._ensure_directories()
    
    def _ensure_directories(self) -> None:
        """确保必要的目录存在"""
        directories = [
            Path(self.database_url.replace("sqlite:///", "")).parent,
            Path(self.log_file).parent,
            Path("./data/cache"),
            Path("./data/temp"),
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
    
    @property
    def database_path(self) -> Path:
        """获取数据库文件路径"""
        return Path(self.database_url.replace("sqlite:///", ""))
    
    def get_temp_dir(self) -> Path:
        """获取临时目录"""
        temp_dir = Path("./data/temp")
        temp_dir.mkdir(parents=True, exist_ok=True)
        return temp_dir
    
    def get_cache_dir(self) -> Path:
        """获取缓存目录"""
        cache_dir = Path("./data/cache")
        cache_dir.mkdir(parents=True, exist_ok=True)
        return cache_dir


# 全局配置实例
config = Config()
