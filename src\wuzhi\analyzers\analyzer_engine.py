"""
文档分析引擎
"""

from pathlib import Path
from typing import Dict, List, Optional
from datetime import datetime

from .base import BaseAnalyzer, AnalysisResult
from .document_type_analyzer import DocumentTypeAnalyzer
from .metadata_analyzer import MetadataAnalyzer
from .content_analyzer import ContentAnalyzer
from ..core.logger import get_logger
from ..extractors.base import ExtractionResult

logger = get_logger(__name__)


class AnalyzerEngine:
    """文档分析引擎"""
    
    def __init__(self):
        self.analyzers = {
            'document_type': DocumentTypeAnalyzer(),
            'metadata': MetadataAnalyzer(),
            'content': ContentAnalyzer(),
        }
        self.analysis_cache = {}
    
    def analyze_document(self, extraction_result: ExtractionResult, 
                        file_path: Path = None, use_cache: bool = True) -> AnalysisResult:
        """完整分析文档"""
        if not extraction_result.success:
            return AnalysisResult(
                success=False,
                error_message="提取结果无效，无法进行分析"
            )
        
        # 检查缓存
        cache_key = self._get_cache_key(extraction_result, file_path)
        if use_cache and cache_key in self.analysis_cache:
            logger.debug(f"使用缓存的分析结果: {cache_key}")
            return self.analysis_cache[cache_key]
        
        try:
            logger.info(f"开始分析文档: {file_path}")
            
            # 执行各个分析器
            type_result = self._run_analyzer('document_type', extraction_result, file_path)
            metadata_result = self._run_analyzer('metadata', extraction_result, file_path)
            content_result = self._run_analyzer('content', extraction_result, file_path)
            
            # 合并分析结果
            final_result = self._merge_analysis_results(
                extraction_result, type_result, metadata_result, content_result
            )
            
            # 后处理
            final_result = self._post_process_result(final_result, extraction_result)
            
            # 缓存结果
            if use_cache:
                self.analysis_cache[cache_key] = final_result
            
            logger.info(f"文档分析完成: {file_path}, 置信度: {final_result.get_overall_confidence():.2f}")
            
            return final_result
            
        except Exception as e:
            logger.error(f"文档分析失败 {file_path}: {e}")
            return AnalysisResult(
                success=False,
                error_message=f"分析失败: {e}"
            )
    
    def _run_analyzer(self, analyzer_name: str, extraction_result: ExtractionResult, 
                     file_path: Path = None) -> AnalysisResult:
        """运行单个分析器"""
        analyzer = self.analyzers.get(analyzer_name)
        if not analyzer:
            logger.warning(f"分析器不存在: {analyzer_name}")
            return AnalysisResult(success=False, error_message=f"分析器不存在: {analyzer_name}")
        
        try:
            logger.debug(f"运行分析器: {analyzer_name}")
            result = analyzer.analyze(extraction_result, file_path)
            
            if result.success:
                logger.debug(f"{analyzer_name} 分析成功，置信度: {result.confidence:.2f}")
            else:
                logger.warning(f"{analyzer_name} 分析失败: {result.error_message}")
            
            return result
            
        except Exception as e:
            logger.error(f"{analyzer_name} 分析器异常: {e}")
            return AnalysisResult(
                success=False,
                error_message=f"{analyzer_name} 分析器异常: {e}"
            )
    
    def _merge_analysis_results(self, extraction_result: ExtractionResult,
                               type_result: AnalysisResult,
                               metadata_result: AnalysisResult,
                               content_result: AnalysisResult) -> AnalysisResult:
        """合并分析结果"""
        merged = AnalysisResult(success=True)
        
        # 合并文档类型信息
        if type_result.success:
            merged.document_type = type_result.document_type
            merged.document_type_confidence = type_result.document_type_confidence
        
        # 合并元数据信息
        if metadata_result.success:
            merged.title = metadata_result.title
            merged.title_confidence = metadata_result.title_confidence
            merged.author = metadata_result.author
            merged.author_confidence = metadata_result.author_confidence
            merged.publisher = metadata_result.publisher
            merged.publish_date = metadata_result.publish_date
            merged.language = metadata_result.language
        
        # 合并内容分析信息
        if content_result.success:
            merged.keywords = content_result.keywords
            merged.summary = content_result.summary
            merged.summary_zh = content_result.summary_zh
            merged.word_count = content_result.word_count
            merged.char_count = content_result.char_count
            merged.main_topics = content_result.main_topics
            merged.text_quality = content_result.text_quality
            
            # 如果元数据分析没有检测到语言，使用内容分析的结果
            if not merged.language and content_result.language:
                merged.language = content_result.language
        
        # 从提取结果中补充信息
        if extraction_result.page_count:
            merged.page_count = extraction_result.page_count
        
        if extraction_result.headings:
            merged.headings = extraction_result.headings
        
        # 合并元数据
        merged.metadata = {
            'extraction_metadata': extraction_result.metadata or {},
            'type_analysis': type_result.metadata if type_result.success else {},
            'metadata_analysis': metadata_result.metadata if metadata_result.success else {},
            'content_analysis': content_result.metadata if content_result.success else {},
            'analysis_timestamp': datetime.now().isoformat(),
        }
        
        return merged
    
    def _post_process_result(self, result: AnalysisResult, 
                           extraction_result: ExtractionResult) -> AnalysisResult:
        """后处理分析结果"""
        # 计算整体置信度
        result.confidence = result.get_overall_confidence()
        
        # 计算完整性评分
        result.completeness = self._calculate_completeness(result)
        
        # 质量检查和修正
        result = self._quality_check_and_fix(result, extraction_result)
        
        return result
    
    def _quality_check_and_fix(self, result: AnalysisResult, 
                              extraction_result: ExtractionResult) -> AnalysisResult:
        """质量检查和修正"""
        # 标题质量检查
        if result.title and len(result.title) > 200:
            # 标题太长，截断
            result.title = result.title[:200] + "..."
            result.title_confidence *= 0.8
        
        # 作者信息检查
        if result.author and len(result.author) > 100:
            # 作者信息太长，可能不准确
            result.author = result.author[:100]
            result.author_confidence *= 0.7
        
        # 关键词质量检查
        if result.keywords:
            # 移除过短或过长的关键词
            filtered_keywords = {
                k: v for k, v in result.keywords.items()
                if 2 <= len(k) <= 20
            }
            result.keywords = filtered_keywords
        
        # 摘要质量检查
        if result.summary:
            # 确保摘要不会太短或太长
            if len(result.summary) < 20:
                # 摘要太短，尝试重新生成
                result.summary = self._generate_fallback_summary(extraction_result.plain_text)
            elif len(result.summary) > 1000:
                # 摘要太长，截断
                result.summary = result.summary[:1000] + "..."
        
        return result
    
    def _generate_fallback_summary(self, text: str) -> str:
        """生成备用摘要"""
        if not text:
            return ""
        
        # 简单地取前200个字符作为摘要
        sentences = text.split('。')[:3]  # 取前3句
        summary = '。'.join(sentences)
        
        if len(summary) > 200:
            summary = summary[:200] + "..."
        
        return summary
    
    def _get_cache_key(self, extraction_result: ExtractionResult, file_path: Path = None) -> str:
        """生成缓存键"""
        import hashlib
        
        # 使用文本内容的哈希作为缓存键
        text_hash = hashlib.md5(extraction_result.plain_text.encode()).hexdigest()
        
        if file_path:
            return f"{file_path.name}_{text_hash}"
        else:
            return f"unknown_{text_hash}"
    
    def batch_analyze(self, extraction_results: Dict[Path, ExtractionResult]) -> Dict[Path, AnalysisResult]:
        """批量分析文档"""
        results = {}
        total = len(extraction_results)
        
        logger.info(f"开始批量分析 {total} 个文档")
        
        for i, (file_path, extraction_result) in enumerate(extraction_results.items(), 1):
            try:
                logger.info(f"分析进度: {i}/{total} - {file_path}")
                
                analysis_result = self.analyze_document(extraction_result, file_path)
                results[file_path] = analysis_result
                
                if analysis_result.success:
                    logger.debug(f"分析成功: {file_path}")
                else:
                    logger.warning(f"分析失败: {file_path} - {analysis_result.error_message}")
                    
            except Exception as e:
                logger.error(f"批量分析异常 {file_path}: {e}")
                results[file_path] = AnalysisResult(
                    success=False,
                    error_message=f"批量分析异常: {e}"
                )
        
        logger.info(f"批量分析完成，成功: {sum(1 for r in results.values() if r.success)}/{total}")
        
        return results
    
    def get_analysis_statistics(self, results: Dict[Path, AnalysisResult]) -> Dict:
        """获取分析统计信息"""
        total = len(results)
        successful = sum(1 for r in results.values() if r.success)
        
        # 统计文档类型分布
        type_distribution = {}
        for result in results.values():
            if result.success and result.document_type:
                doc_type = result.document_type.value
                type_distribution[doc_type] = type_distribution.get(doc_type, 0) + 1
        
        # 统计语言分布
        language_distribution = {}
        for result in results.values():
            if result.success and result.language:
                lang = result.language.value
                language_distribution[lang] = language_distribution.get(lang, 0) + 1
        
        # 计算平均置信度
        confidences = [r.get_overall_confidence() for r in results.values() if r.success]
        avg_confidence = sum(confidences) / len(confidences) if confidences else 0
        
        # 计算平均文本质量
        qualities = [r.text_quality for r in results.values() if r.success and r.text_quality > 0]
        avg_quality = sum(qualities) / len(qualities) if qualities else 0
        
        return {
            'total_documents': total,
            'successful_analyses': successful,
            'success_rate': successful / total if total > 0 else 0,
            'document_type_distribution': type_distribution,
            'language_distribution': language_distribution,
            'average_confidence': avg_confidence,
            'average_text_quality': avg_quality,
            'high_quality_documents': sum(1 for r in results.values() if r.is_high_quality()),
        }
    
    def clear_cache(self):
        """清空分析缓存"""
        self.analysis_cache.clear()
        logger.info("分析缓存已清空")
    
    def register_analyzer(self, name: str, analyzer: BaseAnalyzer):
        """注册自定义分析器"""
        self.analyzers[name] = analyzer
        logger.info(f"注册自定义分析器: {name}")
    
    def get_analyzer_info(self) -> Dict[str, str]:
        """获取分析器信息"""
        return {name: str(analyzer) for name, analyzer in self.analyzers.items()}


# 全局分析引擎实例
analyzer_engine = AnalyzerEngine()
