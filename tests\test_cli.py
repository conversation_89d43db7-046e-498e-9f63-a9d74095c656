"""
命令行界面测试
"""

import pytest
import tempfile
from pathlib import Path
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from unittest.mock import patch, Mock

from src.wuzhi.cli.main import main
from src.wuzhi.cli.commands.init import init_command
from src.wuzhi.cli.commands.scan import scan_command
from src.wuzhi.cli.commands.status import status_command


class TestCLIMain:
    """CLI主入口测试"""
    
    def setup_method(self):
        self.runner = CliRunner()
    
    def test_main_help(self):
        """测试主命令帮助"""
        result = self.runner.invoke(main, ['--help'])
        
        assert result.exit_code == 0
        assert "悟知 (<PERSON><PERSON>hi)" in result.output
        assert "个人知识管理系统" in result.output
    
    def test_main_version(self):
        """测试版本显示"""
        result = self.runner.invoke(main, ['--version'])
        
        assert result.exit_code == 0
        # 版本信息应该包含在输出中
    
    def test_main_debug_flag(self):
        """测试调试标志"""
        result = self.runner.invoke(main, ['--debug', '--help'])
        
        assert result.exit_code == 0
        # 调试模式应该被设置


class TestInitCommand:
    """初始化命令测试"""
    
    def setup_method(self):
        self.runner = CliRunner()
    
    @patch('src.wuzhi.cli.commands.init.init_database')
    @patch('src.wuzhi.cli.commands.init.test_connection')
    def test_init_command_success(self, mock_test_conn, mock_init_db):
        """测试成功初始化"""
        mock_init_db.return_value = True
        mock_test_conn.return_value = True
        
        with self.runner.isolated_filesystem():
            result = self.runner.invoke(init_command)
            
            assert result.exit_code == 0
            assert "初始化完成" in result.output
            mock_init_db.assert_called_once()
            mock_test_conn.assert_called()
    
    @patch('src.wuzhi.cli.commands.init.init_database')
    def test_init_command_failure(self, mock_init_db):
        """测试初始化失败"""
        mock_init_db.return_value = False
        
        with self.runner.isolated_filesystem():
            result = self.runner.invoke(init_command)
            
            assert result.exit_code == 0  # CLI不会因为业务逻辑失败而退出
            assert "初始化失败" in result.output
    
    @patch('src.wuzhi.cli.commands.init.config')
    @patch('src.wuzhi.cli.commands.init.init_database')
    @patch('src.wuzhi.cli.commands.init.test_connection')
    def test_init_force_option(self, mock_test_conn, mock_init_db, mock_config):
        """测试强制初始化选项"""
        mock_init_db.return_value = True
        mock_test_conn.return_value = True
        
        # 模拟数据库文件已存在
        mock_db_path = Mock()
        mock_db_path.exists.return_value = True
        mock_db_path.unlink = Mock()
        mock_config.database_path = mock_db_path
        
        with self.runner.isolated_filesystem():
            result = self.runner.invoke(init_command, ['--force'])
            
            assert result.exit_code == 0
            mock_db_path.unlink.assert_called_once()


class TestScanCommand:
    """扫描命令测试"""
    
    def setup_method(self):
        self.runner = CliRunner()
    
    def test_scan_command_help(self):
        """测试扫描命令帮助"""
        result = self.runner.invoke(scan_command, ['--help'])
        
        assert result.exit_code == 0
        assert "扫描文档目录" in result.output
    
    def test_scan_command_no_paths(self):
        """测试没有提供路径"""
        result = self.runner.invoke(scan_command)
        
        assert result.exit_code != 0  # 应该失败，因为路径是必需的
    
    @patch('src.wuzhi.cli.commands.scan.DocumentScanner')
    def test_scan_command_single_path(self, mock_scanner_class):
        """测试扫描单个路径"""
        mock_scanner = Mock()
        mock_scanner_class.return_value = mock_scanner
        mock_scanner.scan_and_save.return_value = 5
        mock_scanner.get_scan_stats.return_value = {
            'scanned_files': 10,
            'added_files': 5,
            'skipped_files': 3,
            'error_files': 2
        }
        
        with tempfile.TemporaryDirectory() as temp_dir:
            result = self.runner.invoke(scan_command, [temp_dir])
            
            assert result.exit_code == 0
            assert "扫描完成" in result.output
            mock_scanner.scan_and_save.assert_called_once()
    
    @patch('src.wuzhi.cli.commands.scan.DocumentScanner')
    def test_scan_command_dry_run(self, mock_scanner_class):
        """测试试运行模式"""
        mock_scanner = Mock()
        mock_scanner_class.return_value = mock_scanner
        mock_scanner.scan_directory.return_value = [Mock(), Mock(), Mock()]
        mock_scanner.get_scan_stats.return_value = {
            'scanned_files': 3,
            'added_files': 3,
            'skipped_files': 0,
            'error_files': 0
        }
        
        with tempfile.TemporaryDirectory() as temp_dir:
            result = self.runner.invoke(scan_command, [temp_dir, '--dry-run'])
            
            assert result.exit_code == 0
            assert "试运行模式" in result.output
            mock_scanner.scan_directory.assert_called_once()
            # 在试运行模式下不应该调用scan_and_save
            mock_scanner.scan_and_save.assert_not_called()
    
    @patch('src.wuzhi.cli.commands.scan.BatchScanner')
    def test_scan_command_multiple_paths(self, mock_batch_scanner_class):
        """测试扫描多个路径"""
        mock_scanner = Mock()
        mock_batch_scanner_class.return_value = mock_scanner
        mock_scanner.scan_multiple_directories.return_value = 10
        mock_scanner.get_total_stats.return_value = {
            'scanned_files': 20,
            'added_files': 10,
            'skipped_files': 5,
            'error_files': 5
        }
        
        with tempfile.TemporaryDirectory() as temp_dir1:
            with tempfile.TemporaryDirectory() as temp_dir2:
                result = self.runner.invoke(scan_command, [temp_dir1, temp_dir2])
                
                assert result.exit_code == 0
                assert "扫描完成" in result.output
                mock_scanner.scan_multiple_directories.assert_called_once()


class TestStatusCommand:
    """状态命令测试"""
    
    def setup_method(self):
        self.runner = CliRunner()
    
    @patch('src.wuzhi.cli.commands.status.test_connection')
    @patch('src.wuzhi.cli.commands.status.get_db_session')
    def test_status_command_basic(self, mock_session, mock_test_conn):
        """测试基本状态显示"""
        mock_test_conn.return_value = True
        
        # 模拟数据库查询
        mock_query = Mock()
        mock_session.return_value.__enter__.return_value.query.return_value = mock_query
        mock_query.count.return_value = 100
        mock_query.filter.return_value = mock_query
        
        result = self.runner.invoke(status_command)
        
        assert result.exit_code == 0
        assert "系统状态" in result.output
        assert "数据库连接正常" in result.output
    
    @patch('src.wuzhi.cli.commands.status.test_connection')
    def test_status_command_db_failure(self, mock_test_conn):
        """测试数据库连接失败"""
        mock_test_conn.return_value = False
        
        result = self.runner.invoke(status_command)
        
        assert result.exit_code == 0
        assert "数据库连接失败" in result.output
    
    @patch('src.wuzhi.cli.commands.status.test_connection')
    @patch('src.wuzhi.cli.commands.status.get_db_session')
    def test_status_command_detailed(self, mock_session, mock_test_conn):
        """测试详细状态显示"""
        mock_test_conn.return_value = True
        
        # 模拟数据库查询
        mock_query = Mock()
        mock_session.return_value.__enter__.return_value.query.return_value = mock_query
        mock_query.count.return_value = 50
        mock_query.filter.return_value = mock_query
        
        result = self.runner.invoke(status_command, ['--detailed'])
        
        assert result.exit_code == 0
        assert "详细信息" in result.output


class TestSearchCommand:
    """搜索命令测试"""
    
    def setup_method(self):
        self.runner = CliRunner()
    
    @patch('src.wuzhi.cli.main.DocumentService')
    def test_search_command_basic(self, mock_service_class):
        """测试基本搜索"""
        mock_service = Mock()
        mock_service_class.return_value = mock_service
        mock_service.search_documents.return_value = [
            {
                'title': '测试文档',
                'file_name': 'test.txt',
                'author': '测试作者',
                'document_type': 'article',
                'summary': '这是一个测试文档的摘要'
            }
        ]
        
        result = self.runner.invoke(main, ['search', '测试'])
        
        assert result.exit_code == 0
        assert "找到 1 个匹配的文档" in result.output
        assert "测试文档" in result.output
        mock_service.search_documents.assert_called_once()
    
    @patch('src.wuzhi.cli.main.DocumentService')
    def test_search_command_no_results(self, mock_service_class):
        """测试搜索无结果"""
        mock_service = Mock()
        mock_service_class.return_value = mock_service
        mock_service.search_documents.return_value = []
        
        result = self.runner.invoke(main, ['search', '不存在的内容'])
        
        assert result.exit_code == 0
        assert "未找到匹配的文档" in result.output
    
    @patch('src.wuzhi.cli.main.DocumentService')
    def test_search_command_with_filters(self, mock_service_class):
        """测试带过滤条件的搜索"""
        mock_service = Mock()
        mock_service_class.return_value = mock_service
        mock_service.search_documents.return_value = []
        
        result = self.runner.invoke(main, [
            'search', '测试',
            '--type', 'book',
            '--author', '张三',
            '--limit', '5'
        ])
        
        assert result.exit_code == 0
        # 验证调用参数
        call_args = mock_service.search_documents.call_args
        assert call_args[0][0] == '测试'  # query参数
        assert call_args[1]['limit'] == 5
        assert call_args[1]['filters']['document_type'] == 'book'
        assert call_args[1]['filters']['author'] == '张三'


class TestInfoCommand:
    """信息命令测试"""
    
    def setup_method(self):
        self.runner = CliRunner()
    
    @patch('src.wuzhi.cli.main.get_db_session')
    @patch('src.wuzhi.cli.main.config')
    def test_info_command(self, mock_config, mock_session):
        """测试信息显示"""
        mock_config.version = "1.0.0"
        mock_config.config_dir = Path("/test/config")
        mock_config.data_dir = Path("/test/data")
        mock_config.database_path = Path("/test/data/db.sqlite")
        mock_config.log_file = Path("/test/logs/app.log")
        
        # 模拟数据库查询
        mock_query = Mock()
        mock_session.return_value.__enter__.return_value.query.return_value = mock_query
        mock_query.count.return_value = 10
        mock_query.filter.return_value = mock_query
        
        result = self.runner.invoke(main, ['info'])
        
        assert result.exit_code == 0
        assert "悟知 (WuZhi) v1.0.0" in result.output
        assert "数据库统计" in result.output


if __name__ == "__main__":
    pytest.main([__file__])
