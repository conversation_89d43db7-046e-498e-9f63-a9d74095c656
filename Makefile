# 悟知 (<PERSON><PERSON><PERSON>) Makefile

.PHONY: help install dev test lint format clean build docker run gui cli

# 默认目标
.DEFAULT_GOAL := help

# 颜色定义
BLUE := \033[36m
GREEN := \033[32m
YELLOW := \033[33m
RED := \033[31m
NC := \033[0m # No Color

help: ## 显示帮助信息
	@echo "$(BLUE)悟知 (Wu<PERSON><PERSON>) - 个人知识管理系统$(NC)"
	@echo ""
	@echo "$(GREEN)可用命令:$(NC)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(YELLOW)%-15s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST)

install: ## 安装项目依赖
	@echo "$(BLUE)安装项目依赖...$(NC)"
	poetry install
	@echo "$(GREEN)依赖安装完成$(NC)"

dev: ## 安装开发依赖
	@echo "$(BLUE)安装开发依赖...$(NC)"
	poetry install --with dev
	@echo "$(GREEN)开发依赖安装完成$(NC)"

init: ## 初始化项目
	@echo "$(BLUE)初始化项目...$(NC)"
	mkdir -p data logs config cache temp
	@if [ ! -f .env ]; then \
		cp .env.example .env; \
		echo "$(GREEN)配置文件已创建: .env$(NC)"; \
	fi
	poetry run wuzhi init
	@echo "$(GREEN)项目初始化完成$(NC)"

test: ## 运行测试
	@echo "$(BLUE)运行测试...$(NC)"
	poetry run pytest tests/ -v --cov=src --cov-report=term-missing

test-watch: ## 监视文件变化并运行测试
	@echo "$(BLUE)监视测试...$(NC)"
	poetry run pytest-watch tests/ src/

lint: ## 运行代码检查
	@echo "$(BLUE)运行代码检查...$(NC)"
	poetry run black --check src/ tests/
	poetry run isort --check-only src/ tests/
	poetry run flake8 src/ tests/
	poetry run mypy src/

format: ## 格式化代码
	@echo "$(BLUE)格式化代码...$(NC)"
	poetry run black src/ tests/
	poetry run isort src/ tests/
	@echo "$(GREEN)代码格式化完成$(NC)"

clean: ## 清理构建文件
	@echo "$(BLUE)清理构建文件...$(NC)"
	rm -rf build/
	rm -rf dist/
	rm -rf *.egg-info/
	find . -type d -name __pycache__ -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete
	@echo "$(GREEN)清理完成$(NC)"

build: ## 构建项目
	@echo "$(BLUE)构建项目...$(NC)"
	poetry build
	@echo "$(GREEN)构建完成$(NC)"

build-exe: ## 构建可执行文件
	@echo "$(BLUE)构建可执行文件...$(NC)"
	python scripts/build.py --exe
	@echo "$(GREEN)可执行文件构建完成$(NC)"

build-all: ## 构建所有格式
	@echo "$(BLUE)构建所有格式...$(NC)"
	python scripts/build.py --all
	@echo "$(GREEN)所有构建完成$(NC)"

docker-build: ## 构建Docker镜像
	@echo "$(BLUE)构建Docker镜像...$(NC)"
	docker build -t wuzhi:latest .
	@echo "$(GREEN)Docker镜像构建完成$(NC)"

docker-run: ## 运行Docker容器
	@echo "$(BLUE)运行Docker容器...$(NC)"
	docker-compose up -d
	@echo "$(GREEN)Docker容器已启动$(NC)"

docker-stop: ## 停止Docker容器
	@echo "$(BLUE)停止Docker容器...$(NC)"
	docker-compose down
	@echo "$(GREEN)Docker容器已停止$(NC)"

docker-logs: ## 查看Docker日志
	docker-compose logs -f wuzhi

run: gui ## 运行应用（默认GUI模式）

gui: ## 启动图形界面
	@echo "$(BLUE)启动图形界面...$(NC)"
	poetry run wuzhi gui

cli: ## 启动命令行界面
	@echo "$(BLUE)启动命令行界面...$(NC)"
	poetry run wuzhi --help

serve: ## 启动Web服务器
	@echo "$(BLUE)启动Web服务器...$(NC)"
	poetry run wuzhi serve

scan: ## 扫描文档
	@echo "$(BLUE)扫描文档...$(NC)"
	@read -p "请输入要扫描的目录路径: " path; \
	poetry run wuzhi scan "$$path"

analyze: ## 分析文档
	@echo "$(BLUE)分析文档...$(NC)"
	poetry run wuzhi analyze --all

duplicate: ## 检测重复文档
	@echo "$(BLUE)检测重复文档...$(NC)"
	poetry run wuzhi duplicate detect

status: ## 查看系统状态
	@echo "$(BLUE)查看系统状态...$(NC)"
	poetry run wuzhi status --detailed

info: ## 显示系统信息
	@echo "$(BLUE)显示系统信息...$(NC)"
	poetry run wuzhi info

logs: ## 查看日志
	@echo "$(BLUE)查看日志...$(NC)"
	@if [ -f logs/wuzhi.log ]; then \
		tail -f logs/wuzhi.log; \
	else \
		echo "$(YELLOW)日志文件不存在$(NC)"; \
	fi

backup: ## 备份数据
	@echo "$(BLUE)备份数据...$(NC)"
	@timestamp=$$(date +%Y%m%d_%H%M%S); \
	mkdir -p backups; \
	tar -czf backups/wuzhi_backup_$$timestamp.tar.gz data/ config/ .env; \
	echo "$(GREEN)备份完成: backups/wuzhi_backup_$$timestamp.tar.gz$(NC)"

restore: ## 恢复数据
	@echo "$(BLUE)恢复数据...$(NC)"
	@echo "$(YELLOW)可用备份文件:$(NC)"
	@ls -la backups/*.tar.gz 2>/dev/null || echo "$(RED)没有找到备份文件$(NC)"
	@read -p "请输入备份文件名: " backup; \
	if [ -f "backups/$$backup" ]; then \
		tar -xzf "backups/$$backup"; \
		echo "$(GREEN)数据恢复完成$(NC)"; \
	else \
		echo "$(RED)备份文件不存在$(NC)"; \
	fi

update: ## 更新依赖
	@echo "$(BLUE)更新依赖...$(NC)"
	poetry update
	@echo "$(GREEN)依赖更新完成$(NC)"

check: ## 运行所有检查
	@echo "$(BLUE)运行所有检查...$(NC)"
	$(MAKE) lint
	$(MAKE) test
	@echo "$(GREEN)所有检查完成$(NC)"

pre-commit: ## 运行提交前检查
	@echo "$(BLUE)运行提交前检查...$(NC)"
	poetry run pre-commit run --all-files

install-hooks: ## 安装Git钩子
	@echo "$(BLUE)安装Git钩子...$(NC)"
	poetry run pre-commit install
	@echo "$(GREEN)Git钩子安装完成$(NC)"

docs: ## 生成文档
	@echo "$(BLUE)生成文档...$(NC)"
	@echo "$(YELLOW)文档功能待实现$(NC)"

release: ## 创建发布版本
	@echo "$(BLUE)创建发布版本...$(NC)"
	@read -p "请输入版本号 (例如: 0.1.1): " version; \
	poetry version $$version; \
	git add pyproject.toml; \
	git commit -m "Bump version to $$version"; \
	git tag v$$version; \
	echo "$(GREEN)版本 $$version 已创建$(NC)"; \
	echo "$(YELLOW)运行 'git push && git push --tags' 来推送到远程仓库$(NC)"

# 开发环境快速设置
dev-setup: install dev init install-hooks ## 快速设置开发环境
	@echo "$(GREEN)开发环境设置完成$(NC)"

# 生产环境部署
deploy: clean build docker-build ## 部署到生产环境
	@echo "$(GREEN)部署完成$(NC)"
