"""
AI提供者基础类
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Dict, List, Optional, Any
from enum import Enum


class AITaskType(str, Enum):
    """AI任务类型"""
    SUMMARIZE = "summarize"           # 摘要生成
    EXTRACT_METADATA = "extract_metadata"  # 元数据提取
    CLASSIFY_DOCUMENT = "classify_document"  # 文档分类
    EXTRACT_KEYWORDS = "extract_keywords"   # 关键词提取
    TRANSLATE = "translate"           # 翻译
    ANALYZE_CONTENT = "analyze_content"     # 内容分析


@dataclass
class AIResponse:
    """AI响应结果"""
    success: bool = False
    content: str = ""
    metadata: Dict[str, Any] = None
    error_message: Optional[str] = None
    confidence: float = 0.0
    processing_time: float = 0.0
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}
    
    def is_valid(self) -> bool:
        """检查响应是否有效"""
        return self.success and bool(self.content.strip())
    
    def get_summary_dict(self) -> Dict[str, Any]:
        """获取摘要字典"""
        return {
            'success': self.success,
            'content_length': len(self.content) if self.content else 0,
            'confidence': self.confidence,
            'processing_time': self.processing_time,
            'has_metadata': bool(self.metadata),
            'error': self.error_message if not self.success else None,
        }


class BaseAIProvider(ABC):
    """AI提供者基础抽象类"""
    
    def __init__(self, name: str, config: Dict[str, Any] = None):
        self.name = name
        self.config = config or {}
        self.is_available = False
        self.supported_tasks = set()
    
    @abstractmethod
    async def initialize(self) -> bool:
        """初始化AI提供者"""
        pass
    
    @abstractmethod
    async def generate_text(self, prompt: str, **kwargs) -> AIResponse:
        """生成文本"""
        pass
    
    @abstractmethod
    def is_task_supported(self, task_type: AITaskType) -> bool:
        """检查是否支持指定任务类型"""
        pass
    
    @abstractmethod
    async def health_check(self) -> bool:
        """健康检查"""
        pass
    
    async def summarize_text(self, text: str, max_length: int = 200, language: str = "zh") -> AIResponse:
        """生成文本摘要"""
        if not self.is_task_supported(AITaskType.SUMMARIZE):
            return AIResponse(
                success=False,
                error_message=f"{self.name} 不支持摘要生成任务"
            )
        
        from .prompts import PromptManager
        prompt_manager = PromptManager()
        
        prompt = prompt_manager.get_summarize_prompt(text, max_length, language)
        return await self.generate_text(prompt)
    
    async def extract_metadata(self, text: str, document_type: str = None) -> AIResponse:
        """提取文档元数据"""
        if not self.is_task_supported(AITaskType.EXTRACT_METADATA):
            return AIResponse(
                success=False,
                error_message=f"{self.name} 不支持元数据提取任务"
            )
        
        from .prompts import PromptManager
        prompt_manager = PromptManager()
        
        prompt = prompt_manager.get_metadata_extraction_prompt(text, document_type)
        return await self.generate_text(prompt)
    
    async def classify_document(self, text: str) -> AIResponse:
        """文档分类"""
        if not self.is_task_supported(AITaskType.CLASSIFY_DOCUMENT):
            return AIResponse(
                success=False,
                error_message=f"{self.name} 不支持文档分类任务"
            )
        
        from .prompts import PromptManager
        prompt_manager = PromptManager()
        
        prompt = prompt_manager.get_classification_prompt(text)
        return await self.generate_text(prompt)
    
    async def extract_keywords(self, text: str, max_keywords: int = 10) -> AIResponse:
        """提取关键词"""
        if not self.is_task_supported(AITaskType.EXTRACT_KEYWORDS):
            return AIResponse(
                success=False,
                error_message=f"{self.name} 不支持关键词提取任务"
            )
        
        from .prompts import PromptManager
        prompt_manager = PromptManager()
        
        prompt = prompt_manager.get_keyword_extraction_prompt(text, max_keywords)
        return await self.generate_text(prompt)
    
    async def translate_text(self, text: str, target_language: str = "zh") -> AIResponse:
        """翻译文本"""
        if not self.is_task_supported(AITaskType.TRANSLATE):
            return AIResponse(
                success=False,
                error_message=f"{self.name} 不支持翻译任务"
            )
        
        from .prompts import PromptManager
        prompt_manager = PromptManager()
        
        prompt = prompt_manager.get_translation_prompt(text, target_language)
        return await self.generate_text(prompt)
    
    def get_info(self) -> Dict[str, Any]:
        """获取提供者信息"""
        return {
            'name': self.name,
            'is_available': self.is_available,
            'supported_tasks': list(self.supported_tasks),
            'config': {k: v for k, v in self.config.items() if k not in ['api_key', 'token']},  # 隐藏敏感信息
        }
    
    def __str__(self) -> str:
        return f"{self.name} (Available: {self.is_available})"


class AIProviderError(Exception):
    """AI提供者异常"""
    pass


class AIProviderNotAvailableError(AIProviderError):
    """AI提供者不可用异常"""
    pass


class AIProviderTimeoutError(AIProviderError):
    """AI提供者超时异常"""
    pass
