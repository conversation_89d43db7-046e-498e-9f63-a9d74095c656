"""
UI小部件组件
"""

import flet as ft
from pathlib import Path
from typing import Dict, List, Optional, Callable
from datetime import datetime

from ...core.logger import get_logger

logger = get_logger(__name__)


class DocumentCard:
    """文档卡片组件"""
    
    def __init__(self, document_data: Dict, on_click: Callable = None):
        self.document_data = document_data
        self.on_click = on_click
        self.card = None
        self.build_card()
    
    def build_card(self):
        """构建文档卡片"""
        # 文档基本信息
        title = self.document_data.get('title', '无标题')
        file_name = self.document_data.get('file_name', '未知文件')
        author = self.document_data.get('author', '未知作者')
        doc_type = self.document_data.get('document_type', '未知类型')
        file_size = self.document_data.get('file_size', 0)
        is_analyzed = self.document_data.get('is_analyzed', False)
        is_duplicate = self.document_data.get('is_duplicate', False)
        
        # 状态标签
        status_chips = []
        
        if is_analyzed:
            status_chips.append(
                ft.Chip(
                    label=ft.Text("已分析", size=10),
                    bgcolor=ft.colors.GREEN_100,
                    color=ft.colors.GREEN_800,
                )
            )
        else:
            status_chips.append(
                ft.Chip(
                    label=ft.Text("未分析", size=10),
                    bgcolor=ft.colors.ORANGE_100,
                    color=ft.colors.ORANGE_800,
                )
            )
        
        if is_duplicate:
            status_chips.append(
                ft.Chip(
                    label=ft.Text("重复", size=10),
                    bgcolor=ft.colors.RED_100,
                    color=ft.colors.RED_800,
                )
            )
        
        # 文件类型图标
        file_ext = Path(file_name).suffix.lower()
        if file_ext in ['.pdf']:
            icon = ft.icons.PICTURE_AS_PDF
            icon_color = ft.colors.RED
        elif file_ext in ['.docx', '.doc']:
            icon = ft.icons.DESCRIPTION
            icon_color = ft.colors.BLUE
        elif file_ext in ['.txt', '.md']:
            icon = ft.icons.TEXT_SNIPPET
            icon_color = ft.colors.GREEN
        elif file_ext in ['.epub']:
            icon = ft.icons.MENU_BOOK
            icon_color = ft.colors.PURPLE
        else:
            icon = ft.icons.INSERT_DRIVE_FILE
            icon_color = ft.colors.GREY
        
        # 构建卡片内容
        self.card = ft.Card(
            content=ft.Container(
                content=ft.Column(
                    [
                        # 标题行
                        ft.Row(
                            [
                                ft.Icon(icon, color=icon_color, size=20),
                                ft.Expanded(
                                    child=ft.Text(
                                        title,
                                        weight=ft.FontWeight.BOLD,
                                        overflow=ft.TextOverflow.ELLIPSIS,
                                    )
                                ),
                            ],
                            spacing=10,
                        ),
                        
                        # 文件信息
                        ft.Text(
                            file_name,
                            size=12,
                            color=ft.colors.GREY_600,
                            overflow=ft.TextOverflow.ELLIPSIS,
                        ),
                        
                        # 作者和类型
                        ft.Row(
                            [
                                ft.Text(f"作者: {author}", size=11, color=ft.colors.GREY_700),
                                ft.Text(f"类型: {doc_type}", size=11, color=ft.colors.GREY_700),
                            ],
                            spacing=10,
                        ),
                        
                        # 文件大小
                        ft.Text(
                            f"大小: {self._format_size(file_size)}",
                            size=11,
                            color=ft.colors.GREY_700,
                        ),
                        
                        # 状态标签
                        ft.Row(
                            status_chips,
                            spacing=5,
                        ),
                    ],
                    spacing=8,
                    tight=True,
                ),
                padding=15,
                width=280,
                height=160,
            ),
            on_click=self._on_card_click if self.on_click else None,
        )
    
    def _format_size(self, size_bytes: int) -> str:
        """格式化文件大小"""
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        
        return f"{size_bytes:.1f} {size_names[i]}"
    
    def _on_card_click(self, e):
        """卡片点击事件"""
        if self.on_click:
            self.on_click(self.document_data)
    
    def get_control(self) -> ft.Card:
        """获取控件"""
        return self.card


class StatCard:
    """统计卡片组件"""
    
    def __init__(self, title: str, value: str, icon, color: str = None):
        self.title = title
        self.value = value
        self.icon = icon
        self.color = color or ft.colors.BLUE
        self.card = None
        self.build_card()
    
    def build_card(self):
        """构建统计卡片"""
        self.card = ft.Card(
            content=ft.Container(
                content=ft.Row(
                    [
                        ft.Icon(self.icon, size=30, color=self.color),
                        ft.Column(
                            [
                                ft.Text(
                                    self.value,
                                    size=24,
                                    weight=ft.FontWeight.BOLD,
                                ),
                                ft.Text(
                                    self.title,
                                    size=12,
                                    color=ft.colors.GREY_600,
                                ),
                            ],
                            spacing=0,
                        ),
                    ],
                    alignment=ft.MainAxisAlignment.START,
                    vertical_alignment=ft.CrossAxisAlignment.CENTER,
                ),
                padding=15,
                width=150,
                height=80,
            ),
        )
    
    def update_value(self, new_value: str):
        """更新数值"""
        self.value = new_value
        self.build_card()
    
    def get_control(self) -> ft.Card:
        """获取控件"""
        return self.card


class SearchBar:
    """搜索栏组件"""
    
    def __init__(self, on_search: Callable = None, on_filter: Callable = None):
        self.on_search = on_search
        self.on_filter = on_filter
        self.container = None
        
        self.search_field = ft.TextField(
            hint_text="搜索文档...",
            prefix_icon=ft.icons.SEARCH,
            expand=True,
            on_submit=self._on_search_submit,
        )
        
        self.type_filter = ft.Dropdown(
            label="文档类型",
            options=[
                ft.dropdown.Option("all", "全部"),
                ft.dropdown.Option("book", "书籍"),
                ft.dropdown.Option("paper", "论文"),
                ft.dropdown.Option("report", "报告"),
                ft.dropdown.Option("article", "文章"),
                ft.dropdown.Option("manual", "手册"),
            ],
            value="all",
            width=120,
            on_change=self._on_filter_change,
        )
        
        self.status_filter = ft.Dropdown(
            label="状态",
            options=[
                ft.dropdown.Option("all", "全部"),
                ft.dropdown.Option("analyzed", "已分析"),
                ft.dropdown.Option("unanalyzed", "未分析"),
                ft.dropdown.Option("duplicate", "重复"),
            ],
            value="all",
            width=100,
            on_change=self._on_filter_change,
        )
        
        self.build_container()
    
    def build_container(self):
        """构建搜索栏容器"""
        self.container = ft.Container(
            content=ft.Row(
                [
                    self.search_field,
                    self.type_filter,
                    self.status_filter,
                    ft.ElevatedButton(
                        "搜索",
                        icon=ft.icons.SEARCH,
                        on_click=self._on_search_click,
                    ),
                ],
                spacing=10,
            ),
            padding=10,
            bgcolor=ft.colors.SURFACE_VARIANT,
            border_radius=10,
        )
    
    def _on_search_submit(self, e):
        """搜索提交事件"""
        self._perform_search()
    
    def _on_search_click(self, e):
        """搜索按钮点击事件"""
        self._perform_search()
    
    def _perform_search(self):
        """执行搜索"""
        if self.on_search:
            query = self.search_field.value or ""
            filters = {
                'document_type': self.type_filter.value if self.type_filter.value != "all" else None,
                'status': self.status_filter.value if self.status_filter.value != "all" else None,
            }
            self.on_search(query, filters)
    
    def _on_filter_change(self, e):
        """过滤器变化事件"""
        if self.on_filter:
            self._perform_search()
    
    def get_control(self) -> ft.Container:
        """获取控件"""
        return self.container


class FileTree:
    """文件树组件"""
    
    def __init__(self, root_path: Path = None, on_select: Callable = None):
        self.root_path = root_path or Path.home()
        self.on_select = on_select
        self.tree_view = None
        self.build_tree()
    
    def build_tree(self):
        """构建文件树"""
        # 创建根节点
        root_node = ft.TreeNode(
            text=str(self.root_path.name),
            icon=ft.icons.FOLDER,
            expanded=True,
            data=str(self.root_path),
        )
        
        # 添加子节点
        self._add_children(root_node, self.root_path)
        
        self.tree_view = ft.TreeView(
            nodes=[root_node],
            on_select=self._on_node_select,
        )
    
    def _add_children(self, parent_node: ft.TreeNode, path: Path, max_depth: int = 2):
        """添加子节点"""
        if max_depth <= 0:
            return
        
        try:
            for item in sorted(path.iterdir()):
                if item.is_dir() and not item.name.startswith('.'):
                    child_node = ft.TreeNode(
                        text=item.name,
                        icon=ft.icons.FOLDER,
                        data=str(item),
                    )
                    parent_node.nodes.append(child_node)
                    
                    # 递归添加子目录（限制深度）
                    if max_depth > 1:
                        self._add_children(child_node, item, max_depth - 1)
                
                elif item.is_file() and self._is_supported_file(item):
                    file_node = ft.TreeNode(
                        text=item.name,
                        icon=self._get_file_icon(item),
                        data=str(item),
                    )
                    parent_node.nodes.append(file_node)
        
        except PermissionError:
            # 没有权限访问的目录
            pass
    
    def _is_supported_file(self, file_path: Path) -> bool:
        """检查是否为支持的文件类型"""
        supported_extensions = {'.txt', '.pdf', '.docx', '.doc', '.epub', '.md'}
        return file_path.suffix.lower() in supported_extensions
    
    def _get_file_icon(self, file_path: Path):
        """获取文件图标"""
        ext = file_path.suffix.lower()
        if ext == '.pdf':
            return ft.icons.PICTURE_AS_PDF
        elif ext in ['.docx', '.doc']:
            return ft.icons.DESCRIPTION
        elif ext in ['.txt', '.md']:
            return ft.icons.TEXT_SNIPPET
        elif ext == '.epub':
            return ft.icons.MENU_BOOK
        else:
            return ft.icons.INSERT_DRIVE_FILE
    
    def _on_node_select(self, e):
        """节点选择事件"""
        if self.on_select and e.control.selected_nodes:
            selected_node = e.control.selected_nodes[0]
            selected_path = Path(selected_node.data)
            self.on_select(selected_path)
    
    def get_control(self) -> ft.TreeView:
        """获取控件"""
        return self.tree_view
