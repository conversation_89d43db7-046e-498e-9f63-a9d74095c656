#!/usr/bin/env python3
"""
悟知 (<PERSON><PERSON><PERSON>) 一键安装脚本
"""

import os
import sys
import subprocess
import platform
from pathlib import Path


def run_command(command, check=True, shell=False):
    """运行命令"""
    print(f"执行命令: {command}")
    try:
        if isinstance(command, str) and not shell:
            command = command.split()
        
        result = subprocess.run(
            command,
            check=check,
            capture_output=True,
            text=True,
            shell=shell
        )
        
        if result.stdout:
            print(result.stdout)
        
        return result
    except subprocess.CalledProcessError as e:
        print(f"命令执行失败: {e}")
        if e.stderr:
            print(f"错误信息: {e.stderr}")
        if check:
            sys.exit(1)
        return e


def check_python_version():
    """检查Python版本"""
    print("检查Python版本...")
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 9):
        print("错误: 需要Python 3.9或更高版本")
        print(f"当前版本: {version.major}.{version.minor}.{version.micro}")
        sys.exit(1)
    print(f"Python版本检查通过: {version.major}.{version.minor}.{version.micro}")


def check_poetry():
    """检查Poetry是否安装"""
    print("检查Poetry...")
    try:
        result = run_command("poetry --version", check=False)
        if result.returncode == 0:
            print("Poetry已安装")
            return True
    except FileNotFoundError:
        pass
    
    print("Poetry未安装，正在安装...")
    install_poetry()
    return True


def install_poetry():
    """安装Poetry"""
    system = platform.system().lower()
    
    if system == "windows":
        # Windows安装
        command = 'powershell -Command "(Invoke-WebRequest -Uri https://install.python-poetry.org -UseBasicParsing).Content | python -"'
        run_command(command, shell=True)
    else:
        # Linux/macOS安装
        command = "curl -sSL https://install.python-poetry.org | python3 -"
        run_command(command, shell=True)
    
    print("Poetry安装完成")


def install_system_dependencies():
    """安装系统依赖"""
    print("检查系统依赖...")
    system = platform.system().lower()
    
    if system == "linux":
        # 检查是否为Ubuntu/Debian
        try:
            run_command("which apt-get", check=False)
            print("检测到Ubuntu/Debian系统，安装依赖...")
            run_command("sudo apt-get update", shell=True)
            run_command("sudo apt-get install -y tesseract-ocr tesseract-ocr-chi-sim tesseract-ocr-eng", shell=True)
            run_command("sudo apt-get install -y libmagic1", shell=True)
        except:
            print("请手动安装tesseract-ocr和libmagic")
    
    elif system == "darwin":  # macOS
        try:
            run_command("which brew", check=False)
            print("检测到macOS系统，使用Homebrew安装依赖...")
            run_command("brew install tesseract", shell=True)
            run_command("brew install libmagic", shell=True)
        except:
            print("请安装Homebrew并手动安装tesseract和libmagic")
    
    elif system == "windows":
        print("Windows系统请手动安装以下依赖:")
        print("1. Tesseract OCR: https://github.com/UB-Mannheim/tesseract/wiki")
        print("2. 将Tesseract添加到PATH环境变量")
        print("3. 下载中文语言包")


def install_ollama():
    """安装Ollama（可选）"""
    print("\n是否安装Ollama AI模型支持? (y/n): ", end="")
    choice = input().lower().strip()
    
    if choice not in ['y', 'yes']:
        print("跳过Ollama安装")
        return
    
    system = platform.system().lower()
    
    if system == "linux":
        print("安装Ollama...")
        run_command("curl -fsSL https://ollama.ai/install.sh | sh", shell=True)
    elif system == "darwin":
        print("请访问 https://ollama.ai 下载macOS版本")
    elif system == "windows":
        print("请访问 https://ollama.ai 下载Windows版本")
    
    print("Ollama安装完成后，请运行以下命令下载模型:")
    print("ollama pull qwen:4b")


def install_project():
    """安装项目依赖"""
    print("安装项目依赖...")
    
    # 安装Python依赖
    run_command("poetry install")
    
    # 初始化数据库
    print("初始化数据库...")
    run_command("poetry run wuzhi init")
    
    print("项目安装完成!")


def create_desktop_shortcut():
    """创建桌面快捷方式（可选）"""
    print("\n是否创建桌面快捷方式? (y/n): ", end="")
    choice = input().lower().strip()
    
    if choice not in ['y', 'yes']:
        return
    
    system = platform.system().lower()
    project_dir = Path.cwd()
    
    if system == "windows":
        # Windows快捷方式
        desktop = Path.home() / "Desktop"
        shortcut_path = desktop / "悟知.bat"
        
        with open(shortcut_path, 'w', encoding='utf-8') as f:
            f.write(f'@echo off\n')
            f.write(f'cd /d "{project_dir}"\n')
            f.write(f'poetry run wuzhi gui\n')
            f.write(f'pause\n')
        
        print(f"桌面快捷方式已创建: {shortcut_path}")
    
    elif system == "linux":
        # Linux .desktop文件
        desktop = Path.home() / "Desktop"
        shortcut_path = desktop / "wuzhi.desktop"
        
        with open(shortcut_path, 'w') as f:
            f.write(f'[Desktop Entry]\n')
            f.write(f'Name=悟知 (WuZhi)\n')
            f.write(f'Comment=个人知识管理系统\n')
            f.write(f'Exec=bash -c "cd {project_dir} && poetry run wuzhi gui"\n')
            f.write(f'Icon={project_dir}/assets/icon.png\n')
            f.write(f'Terminal=false\n')
            f.write(f'Type=Application\n')
            f.write(f'Categories=Office;Education;\n')
        
        # 设置可执行权限
        os.chmod(shortcut_path, 0o755)
        print(f"桌面快捷方式已创建: {shortcut_path}")


def main():
    """主安装流程"""
    print("=" * 50)
    print("悟知 (WuZhi) 个人知识管理系统")
    print("一键安装脚本")
    print("=" * 50)
    
    # 检查Python版本
    check_python_version()
    
    # 检查并安装Poetry
    check_poetry()
    
    # 安装系统依赖
    install_system_dependencies()
    
    # 安装项目依赖
    install_project()
    
    # 安装Ollama（可选）
    install_ollama()
    
    # 创建桌面快捷方式（可选）
    create_desktop_shortcut()
    
    print("\n" + "=" * 50)
    print("安装完成!")
    print("=" * 50)
    print("使用方法:")
    print("1. 启动图形界面: poetry run wuzhi gui")
    print("2. 扫描文档: poetry run wuzhi scan /path/to/documents")
    print("3. 查看帮助: poetry run wuzhi --help")
    print("=" * 50)


if __name__ == "__main__":
    main()
