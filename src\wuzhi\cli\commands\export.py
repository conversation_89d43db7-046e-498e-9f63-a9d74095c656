"""
数据导出命令
"""

import click
import json
import csv
from pathlib import Path
from datetime import datetime

from ...core.database import get_db_session
from ...core.models import Document
from ...core.logger import get_logger

logger = get_logger(__name__)


@click.group('export')
@click.pass_context
def export_command(ctx):
    """导出数据"""
    pass


@export_command.command('json')
@click.argument('output_file', type=click.Path())
@click.option('--include-content', is_flag=True, help='包含文档内容')
@click.option('--analyzed-only', is_flag=True, help='只导出已分析的文档')
@click.option('--limit', type=int, help='限制导出数量')
@click.pass_context
def json_command(ctx, output_file, include_content, analyzed_only, limit):
    """导出为JSON格式"""
    try:
        click.echo("正在导出数据为JSON格式...")
        
        # 获取文档数据
        documents = _get_documents_for_export(analyzed_only, limit)
        
        if not documents:
            click.echo("没有找到要导出的文档")
            return
        
        click.echo(f"准备导出 {len(documents)} 个文档")
        
        # 转换为字典格式
        export_data = {
            'export_info': {
                'timestamp': datetime.now().isoformat(),
                'total_documents': len(documents),
                'include_content': include_content,
                'analyzed_only': analyzed_only,
            },
            'documents': []
        }
        
        for doc in documents:
            doc_data = {
                'id': doc.id,
                'file_name': doc.file_name,
                'file_path': doc.file_path,
                'file_size': doc.file_size,
                'file_type': doc.file_type,
                'file_hash': doc.file_hash,
                'title': doc.title,
                'author': doc.author,
                'document_type': doc.document_type,
                'language': doc.language,
                'is_analyzed': doc.is_analyzed,
                'is_duplicate': doc.is_duplicate,
                'created_at': doc.created_at.isoformat() if doc.created_at else None,
                'updated_at': doc.updated_at.isoformat() if doc.updated_at else None,
            }
            
            # 包含内容（如果请求）
            if include_content and hasattr(doc, 'content'):
                doc_data['content'] = doc.content
            
            export_data['documents'].append(doc_data)
        
        # 写入文件
        output_path = Path(output_file)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, ensure_ascii=False, indent=2)
        
        click.echo(f"✓ 数据已导出到: {output_path}")
        click.echo(f"文件大小: {_format_size(output_path.stat().st_size)}")
        
    except Exception as e:
        click.echo(f"JSON导出失败: {e}", err=True)
        logger.error(f"JSON导出失败: {e}")


@export_command.command('csv')
@click.argument('output_file', type=click.Path())
@click.option('--analyzed-only', is_flag=True, help='只导出已分析的文档')
@click.option('--limit', type=int, help='限制导出数量')
@click.pass_context
def csv_command(ctx, output_file, analyzed_only, limit):
    """导出为CSV格式"""
    try:
        click.echo("正在导出数据为CSV格式...")
        
        # 获取文档数据
        documents = _get_documents_for_export(analyzed_only, limit)
        
        if not documents:
            click.echo("没有找到要导出的文档")
            return
        
        click.echo(f"准备导出 {len(documents)} 个文档")
        
        # 写入CSV文件
        output_path = Path(output_file)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            
            # 写入表头
            headers = [
                'ID', '文件名', '文件路径', '文件大小', '文件类型', '文件哈希',
                '标题', '作者', '文档类型', '语言', '是否已分析', '是否重复',
                '创建时间', '更新时间'
            ]
            writer.writerow(headers)
            
            # 写入数据
            for doc in documents:
                row = [
                    doc.id,
                    doc.file_name,
                    doc.file_path,
                    doc.file_size,
                    doc.file_type,
                    doc.file_hash,
                    doc.title or '',
                    doc.author or '',
                    doc.document_type or '',
                    doc.language or '',
                    '是' if doc.is_analyzed else '否',
                    '是' if doc.is_duplicate else '否',
                    doc.created_at.isoformat() if doc.created_at else '',
                    doc.updated_at.isoformat() if doc.updated_at else '',
                ]
                writer.writerow(row)
        
        click.echo(f"✓ 数据已导出到: {output_path}")
        click.echo(f"文件大小: {_format_size(output_path.stat().st_size)}")
        
    except Exception as e:
        click.echo(f"CSV导出失败: {e}", err=True)
        logger.error(f"CSV导出失败: {e}")


@export_command.command('summary')
@click.argument('output_file', type=click.Path())
@click.option('--format', 'output_format', type=click.Choice(['txt', 'md', 'html']), default='txt', help='输出格式')
@click.pass_context
def summary_command(ctx, output_file, output_format):
    """导出系统摘要报告"""
    try:
        click.echo("正在生成系统摘要报告...")
        
        # 收集统计信息
        stats = _collect_system_statistics()
        
        # 生成报告内容
        if output_format == 'md':
            content = _generate_markdown_report(stats)
        elif output_format == 'html':
            content = _generate_html_report(stats)
        else:
            content = _generate_text_report(stats)
        
        # 写入文件
        output_path = Path(output_file)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        click.echo(f"✓ 摘要报告已生成: {output_path}")
        click.echo(f"格式: {output_format.upper()}")
        
    except Exception as e:
        click.echo(f"摘要报告生成失败: {e}", err=True)
        logger.error(f"摘要报告生成失败: {e}")


def _get_documents_for_export(analyzed_only=False, limit=None):
    """获取要导出的文档"""
    try:
        with get_db_session() as session:
            query = session.query(Document)
            
            if analyzed_only:
                query = query.filter(Document.is_analyzed == True)
            
            if limit:
                query = query.limit(limit)
            
            return query.all()
    
    except Exception as e:
        logger.error(f"获取导出文档失败: {e}")
        return []


def _collect_system_statistics():
    """收集系统统计信息"""
    try:
        with get_db_session() as session:
            from sqlalchemy import func
            
            # 基本统计
            total_docs = session.query(Document).count()
            analyzed_docs = session.query(Document).filter(Document.is_analyzed == True).count()
            duplicate_docs = session.query(Document).filter(Document.is_duplicate == True).count()
            
            # 文档类型分布
            type_stats = session.query(
                Document.document_type,
                func.count(Document.id).label('count')
            ).filter(
                Document.document_type.isnot(None)
            ).group_by(
                Document.document_type
            ).all()
            
            # 语言分布
            lang_stats = session.query(
                Document.language,
                func.count(Document.id).label('count')
            ).filter(
                Document.language.isnot(None)
            ).group_by(
                Document.language
            ).all()
            
            # 文件类型分布
            file_type_stats = session.query(
                Document.file_type,
                func.count(Document.id).label('count')
            ).group_by(
                Document.file_type
            ).all()
            
            return {
                'total_documents': total_docs,
                'analyzed_documents': analyzed_docs,
                'duplicate_documents': duplicate_docs,
                'unique_documents': total_docs - duplicate_docs,
                'document_types': dict(type_stats),
                'languages': dict(lang_stats),
                'file_types': dict(file_type_stats),
                'analysis_rate': (analyzed_docs / total_docs * 100) if total_docs > 0 else 0,
                'duplicate_rate': (duplicate_docs / total_docs * 100) if total_docs > 0 else 0,
            }
    
    except Exception as e:
        logger.error(f"收集统计信息失败: {e}")
        return {}


def _generate_text_report(stats):
    """生成文本格式报告"""
    report = []
    report.append("悟知 (WuZhi) 系统摘要报告")
    report.append("=" * 40)
    report.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report.append("")
    
    # 基本统计
    report.append("基本统计:")
    report.append("-" * 20)
    report.append(f"总文档数: {stats.get('total_documents', 0)}")
    report.append(f"已分析: {stats.get('analyzed_documents', 0)}")
    report.append(f"重复文档: {stats.get('duplicate_documents', 0)}")
    report.append(f"唯一文档: {stats.get('unique_documents', 0)}")
    report.append(f"分析率: {stats.get('analysis_rate', 0):.1f}%")
    report.append(f"重复率: {stats.get('duplicate_rate', 0):.1f}%")
    report.append("")
    
    # 文档类型分布
    doc_types = stats.get('document_types', {})
    if doc_types:
        report.append("文档类型分布:")
        report.append("-" * 20)
        for doc_type, count in doc_types.items():
            report.append(f"{doc_type}: {count}")
        report.append("")
    
    # 语言分布
    languages = stats.get('languages', {})
    if languages:
        report.append("语言分布:")
        report.append("-" * 20)
        for lang, count in languages.items():
            report.append(f"{lang}: {count}")
        report.append("")
    
    # 文件类型分布
    file_types = stats.get('file_types', {})
    if file_types:
        report.append("文件类型分布:")
        report.append("-" * 20)
        for file_type, count in file_types.items():
            report.append(f"{file_type}: {count}")
    
    return "\n".join(report)


def _generate_markdown_report(stats):
    """生成Markdown格式报告"""
    report = []
    report.append("# 悟知 (WuZhi) 系统摘要报告")
    report.append("")
    report.append(f"**生成时间:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report.append("")
    
    # 基本统计
    report.append("## 基本统计")
    report.append("")
    report.append("| 项目 | 数量 |")
    report.append("|------|------|")
    report.append(f"| 总文档数 | {stats.get('total_documents', 0)} |")
    report.append(f"| 已分析 | {stats.get('analyzed_documents', 0)} |")
    report.append(f"| 重复文档 | {stats.get('duplicate_documents', 0)} |")
    report.append(f"| 唯一文档 | {stats.get('unique_documents', 0)} |")
    report.append(f"| 分析率 | {stats.get('analysis_rate', 0):.1f}% |")
    report.append(f"| 重复率 | {stats.get('duplicate_rate', 0):.1f}% |")
    report.append("")
    
    # 文档类型分布
    doc_types = stats.get('document_types', {})
    if doc_types:
        report.append("## 文档类型分布")
        report.append("")
        report.append("| 类型 | 数量 |")
        report.append("|------|------|")
        for doc_type, count in doc_types.items():
            report.append(f"| {doc_type} | {count} |")
        report.append("")
    
    return "\n".join(report)


def _generate_html_report(stats):
    """生成HTML格式报告"""
    html = f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>悟知系统摘要报告</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 40px; }}
        h1, h2 {{ color: #333; }}
        table {{ border-collapse: collapse; width: 100%; margin: 20px 0; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background-color: #f2f2f2; }}
    </style>
</head>
<body>
    <h1>悟知 (WuZhi) 系统摘要报告</h1>
    <p><strong>生成时间:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
    
    <h2>基本统计</h2>
    <table>
        <tr><th>项目</th><th>数量</th></tr>
        <tr><td>总文档数</td><td>{stats.get('total_documents', 0)}</td></tr>
        <tr><td>已分析</td><td>{stats.get('analyzed_documents', 0)}</td></tr>
        <tr><td>重复文档</td><td>{stats.get('duplicate_documents', 0)}</td></tr>
        <tr><td>唯一文档</td><td>{stats.get('unique_documents', 0)}</td></tr>
        <tr><td>分析率</td><td>{stats.get('analysis_rate', 0):.1f}%</td></tr>
        <tr><td>重复率</td><td>{stats.get('duplicate_rate', 0):.1f}%</td></tr>
    </table>
</body>
</html>
"""
    return html


def _format_size(size_bytes):
    """格式化文件大小"""
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f} {size_names[i]}"
