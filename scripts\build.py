#!/usr/bin/env python3
"""
悟知 (Wu<PERSON><PERSON>) 构建脚本
用于构建可分发的应用程序包
"""

import os
import sys
import shutil
import subprocess
import argparse
from pathlib import Path
from typing import List, Optional

# 项目根目录
PROJECT_ROOT = Path(__file__).parent.parent
BUILD_DIR = PROJECT_ROOT / "build"
DIST_DIR = PROJECT_ROOT / "dist"


def run_command(cmd: List[str], cwd: Optional[Path] = None) -> int:
    """运行命令"""
    print(f"运行命令: {' '.join(cmd)}")
    result = subprocess.run(cmd, cwd=cwd or PROJECT_ROOT)
    return result.returncode


def clean_build():
    """清理构建目录"""
    print("清理构建目录...")
    
    for directory in [BUILD_DIR, DIST_DIR]:
        if directory.exists():
            shutil.rmtree(directory)
            print(f"已删除: {directory}")
    
    # 清理Python缓存
    for cache_dir in PROJECT_ROOT.rglob("__pycache__"):
        shutil.rmtree(cache_dir)
    
    for pyc_file in PROJECT_ROOT.rglob("*.pyc"):
        pyc_file.unlink()
    
    print("构建目录清理完成")


def build_wheel():
    """构建Python wheel包"""
    print("构建Python wheel包...")
    
    result = run_command(["poetry", "build"])
    if result != 0:
        print("构建wheel包失败")
        return False
    
    print("wheel包构建完成")
    return True


def build_executable():
    """构建可执行文件"""
    print("构建可执行文件...")
    
    # 检查PyInstaller是否安装
    try:
        import PyInstaller
    except ImportError:
        print("安装PyInstaller...")
        result = run_command(["poetry", "add", "--group", "dev", "pyinstaller"])
        if result != 0:
            print("PyInstaller安装失败")
            return False
    
    # PyInstaller配置
    spec_content = f'''
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['src/wuzhi/cli/main.py'],
    pathex=['{PROJECT_ROOT}'],
    binaries=[],
    datas=[
        ('src/wuzhi/ui/assets', 'wuzhi/ui/assets'),
        ('.env.example', '.'),
    ],
    hiddenimports=[
        'wuzhi.core',
        'wuzhi.ai',
        'wuzhi.services',
        'wuzhi.ui',
        'flet',
        'sqlalchemy.dialects.sqlite',
    ],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='wuzhi',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''
    
    # 写入spec文件
    spec_file = PROJECT_ROOT / "wuzhi.spec"
    with open(spec_file, "w", encoding="utf-8") as f:
        f.write(spec_content)
    
    # 运行PyInstaller
    result = run_command(["poetry", "run", "pyinstaller", "wuzhi.spec", "--clean"])
    if result != 0:
        print("可执行文件构建失败")
        return False
    
    print("可执行文件构建完成")
    return True


def build_installer():
    """构建安装程序"""
    print("构建安装程序...")
    
    # 这里可以添加NSIS或其他安装程序构建逻辑
    # 目前只是创建一个简单的压缩包
    
    import zipfile
    
    # 创建分发目录
    dist_content = DIST_DIR / "wuzhi-portable"
    dist_content.mkdir(parents=True, exist_ok=True)
    
    # 复制可执行文件
    exe_file = DIST_DIR / "wuzhi.exe" if sys.platform == "win32" else DIST_DIR / "wuzhi"
    if exe_file.exists():
        shutil.copy2(exe_file, dist_content)
    
    # 复制配置文件
    shutil.copy2(PROJECT_ROOT / ".env.example", dist_content)
    shutil.copy2(PROJECT_ROOT / "README.md", dist_content)
    
    # 创建启动脚本
    if sys.platform == "win32":
        start_script = dist_content / "start.bat"
        with open(start_script, "w", encoding="utf-8") as f:
            f.write("@echo off\n")
            f.write("wuzhi.exe gui\n")
            f.write("pause\n")
    else:
        start_script = dist_content / "start.sh"
        with open(start_script, "w", encoding="utf-8") as f:
            f.write("#!/bin/bash\n")
            f.write("./wuzhi gui\n")
        start_script.chmod(0o755)
    
    # 创建压缩包
    zip_file = DIST_DIR / f"wuzhi-portable-{sys.platform}.zip"
    with zipfile.ZipFile(zip_file, "w", zipfile.ZIP_DEFLATED) as zf:
        for file_path in dist_content.rglob("*"):
            if file_path.is_file():
                arcname = file_path.relative_to(dist_content)
                zf.write(file_path, arcname)
    
    print(f"安装程序已创建: {zip_file}")
    return True


def run_tests():
    """运行测试"""
    print("运行测试...")
    
    result = run_command(["poetry", "run", "pytest", "tests/", "-v", "--cov=src"])
    if result != 0:
        print("测试失败")
        return False
    
    print("测试通过")
    return True


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="悟知 (WuZhi) 构建脚本")
    parser.add_argument("--clean", action="store_true", help="清理构建目录")
    parser.add_argument("--wheel", action="store_true", help="构建wheel包")
    parser.add_argument("--exe", action="store_true", help="构建可执行文件")
    parser.add_argument("--installer", action="store_true", help="构建安装程序")
    parser.add_argument("--test", action="store_true", help="运行测试")
    parser.add_argument("--all", action="store_true", help="执行所有构建步骤")
    
    args = parser.parse_args()
    
    # 如果没有指定参数，显示帮助
    if not any(vars(args).values()):
        parser.print_help()
        return
    
    success = True
    
    try:
        if args.clean or args.all:
            clean_build()
        
        if args.test or args.all:
            if not run_tests():
                success = False
        
        if args.wheel or args.all:
            if not build_wheel():
                success = False
        
        if args.exe or args.all:
            if not build_executable():
                success = False
        
        if args.installer or args.all:
            if not build_installer():
                success = False
        
        if success:
            print("\n构建完成！")
            
            # 显示构建结果
            if DIST_DIR.exists():
                print(f"\n构建产物位于: {DIST_DIR}")
                for item in DIST_DIR.iterdir():
                    print(f"  - {item.name}")
        else:
            print("\n构建过程中出现错误")
            sys.exit(1)
    
    except KeyboardInterrupt:
        print("\n构建被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n构建失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
